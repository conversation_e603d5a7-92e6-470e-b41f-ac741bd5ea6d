export type SlideElement =
  | {
      type: "text";
      id: string;
      x: number;
      y: number;
      width: number;
      height: number;
      text: string;
      style: {
        fontSize: number;
        color: string;
        backgroundColor?: string;
        fontFamily?: string;
        bold?: boolean;
        italic?: boolean;
        textAlign?: "left" | "center" | "right";
      };
    }
  | {
      type: "image";
      id: string;
      x: number;
      y: number;
      width: number;
      height: number;
      src: string;
      alt?: string;
    }
  | {
      type: "shape";
      id: string;
      x: number;
      y: number;
      width: number;
      height: number;
      shapeType: string;
      svgPath: string;
      fill?: string;
      stroke?: string;
      strokeWidth?: number;
    }
  | {
      type: "table";
      id: string;
      x: number;
      y: number;
      width: number;
      height: number;
      rows: string[][];
      style?: {
        borderColor?: string;
        backgroundColor?: string;
        fontSize?: number;
      };
    };

export interface Slide {
  id: string;
  name: string;
  elements: SlideElement[];
}

export interface SlideEditorState {
  slides: Slide[];
  currentSlideId: string;
  selectedElementId: string | null;
  canvasSize: {
    width: number;
    height: number;
  };
}

export interface ElementStyle {
  fontSize?: number;
  color?: string;
  backgroundColor?: string;
  fontFamily?: string;
  bold?: boolean;
  italic?: boolean;
  textAlign?: "left" | "center" | "right";
}
