"use client";

import { useState, useRef } from "react";
import { Rnd } from "react-rnd";
import { SlideElement, SlideEditorState } from "@/types/slideEditor";
import {
  Plus,
  Type,
  Image,
  Square,
  Grid3X3,
  Download,
  Upload,
} from "lucide-react";

export default function SlideEditorPage() {
  const [state, setState] = useState<SlideEditorState>({
    elements: [],
    selectedElementId: null,
    canvasSize: { width: 800, height: 600 },
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Add new element
  const addElement = (type: SlideElement["type"]) => {
    const newElement: SlideElement = {
      id: `${type}-${Date.now()}`,
      type,
      x: 50,
      y: 50,
      width: type === "text" ? 200 : type === "table" ? 300 : 150,
      height: type === "text" ? 50 : type === "table" ? 200 : 100,
      ...(type === "text" && {
        text: "Click to edit text",
        style: {
          fontSize: 16,
          color: "#000000",
          fontFamily: "Arial",
          textAlign: "left" as const,
        },
      }),
      ...(type === "image" && {
        src: "https://via.placeholder.com/150x100?text=Image",
        alt: "Placeholder image",
      }),
      ...(type === "shape" && {
        shapeType: "rectangle",
        svgPath: "",
        fill: "#3B82F6",
        stroke: "#1E40AF",
        strokeWidth: 2,
      }),
      ...(type === "table" && {
        rows: [
          ["Header 1", "Header 2", "Header 3"],
          ["Row 1 Col 1", "Row 1 Col 2", "Row 1 Col 3"],
          ["Row 2 Col 1", "Row 2 Col 2", "Row 2 Col 3"],
        ],
        style: {
          borderColor: "#000000",
          backgroundColor: "#FFFFFF",
          fontSize: 14,
        },
      }),
    } as SlideElement;

    setState((prev) => ({
      ...prev,
      elements: [...prev.elements, newElement],
      selectedElementId: newElement.id,
    }));
  };

  // Update element
  const updateElement = (id: string, updates: Partial<SlideElement>) => {
    setState((prev) => ({
      ...prev,
      elements: prev.elements.map((el) =>
        el.id === id ? { ...el, ...updates } : el
      ),
    }));
  };

  // Delete element
  const deleteElement = (id: string) => {
    setState((prev) => ({
      ...prev,
      elements: prev.elements.filter((el) => el.id !== id),
      selectedElementId:
        prev.selectedElementId === id ? null : prev.selectedElementId,
    }));
  };

  // Handle image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const newElement: SlideElement = {
          id: `image-${Date.now()}`,
          type: "image",
          x: 50,
          y: 50,
          width: 200,
          height: 150,
          src: e.target?.result as string,
          alt: file.name,
        };
        setState((prev) => ({
          ...prev,
          elements: [...prev.elements, newElement],
          selectedElementId: newElement.id,
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  // Export to JSON
  const exportToJSON = () => {
    const exportData = {
      canvasSize: state.canvasSize,
      elements: state.elements,
    };
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "slide-design.json";
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="h-screen flex bg-gray-100">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg p-4 space-y-4">
        <h2 className="text-lg font-bold text-gray-800">Elements</h2>

        {/* Add Elements */}
        <div className="space-y-2">
          <button
            onClick={() => addElement("text")}
            className="w-full flex items-center gap-2 p-3 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            <Type size={16} />
            Add Text
          </button>

          <button
            onClick={() => fileInputRef.current?.click()}
            className="w-full flex items-center gap-2 p-3 bg-green-500 text-white rounded hover:bg-green-600"
          >
            <Image size={16} />
            Add Image
          </button>

          <button
            onClick={() => addElement("shape")}
            className="w-full flex items-center gap-2 p-3 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            <Square size={16} />
            Add Shape
          </button>

          <button
            onClick={() => addElement("table")}
            className="w-full flex items-center gap-2 p-3 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            <Grid3X3 size={16} />
            Add Table
          </button>
        </div>

        {/* Export */}
        <div className="pt-4 border-t">
          <button
            onClick={exportToJSON}
            className="w-full flex items-center gap-2 p-3 bg-gray-700 text-white rounded hover:bg-gray-800"
          >
            <Download size={16} />
            Export JSON
          </button>
        </div>

        {/* Element List */}
        <div className="pt-4 border-t">
          <h3 className="font-semibold text-gray-700 mb-2">
            Elements ({state.elements.length})
          </h3>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {state.elements.map((element) => (
              <div
                key={element.id}
                className={`p-2 rounded text-sm cursor-pointer ${
                  state.selectedElementId === element.id
                    ? "bg-blue-100 text-blue-800"
                    : "bg-gray-50 hover:bg-gray-100"
                }`}
                onClick={() =>
                  setState((prev) => ({
                    ...prev,
                    selectedElementId: element.id,
                  }))
                }
              >
                <div className="flex justify-between items-center">
                  <span className="capitalize">{element.type}</span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteElement(element.id);
                    }}
                    className="text-red-500 hover:text-red-700"
                  >
                    ×
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />

      {/* Canvas */}
      <div className="flex-1 p-4">
        <div
          className="relative bg-white shadow-lg mx-auto"
          style={{
            width: state.canvasSize.width,
            height: state.canvasSize.height,
          }}
        >
          {state.elements.map((element) => (
            <ElementRenderer
              key={element.id}
              element={element}
              isSelected={state.selectedElementId === element.id}
              onUpdate={(updates) => updateElement(element.id, updates)}
              onSelect={() =>
                setState((prev) => ({ ...prev, selectedElementId: element.id }))
              }
            />
          ))}
        </div>
      </div>

      {/* Style Panel */}
      {state.selectedElementId && (
        <StylePanel
          element={
            state.elements.find((el) => el.id === state.selectedElementId)!
          }
          onUpdate={(updates) =>
            updateElement(state.selectedElementId!, updates)
          }
        />
      )}
    </div>
  );
}

// Element Renderer Component
function ElementRenderer({
  element,
  isSelected,
  onUpdate,
  onSelect,
}: {
  element: SlideElement;
  isSelected: boolean;
  onUpdate: (updates: Partial<SlideElement>) => void;
  onSelect: () => void;
}) {
  return (
    <Rnd
      size={{ width: element.width, height: element.height }}
      position={{ x: element.x, y: element.y }}
      onDragStop={(e, d) => {
        onUpdate({ x: d.x, y: d.y });
      }}
      onResizeStop={(e, direction, ref, delta, position) => {
        onUpdate({
          width: ref.offsetWidth,
          height: ref.offsetHeight,
          x: position.x,
          y: position.y,
        });
      }}
      onClick={onSelect}
      className={`${isSelected ? "ring-2 ring-blue-500" : ""}`}
    >
      <ElementContent element={element} onUpdate={onUpdate} />
    </Rnd>
  );
}

// Element Content Component
function ElementContent({
  element,
  onUpdate,
}: {
  element: SlideElement;
  onUpdate: (updates: Partial<SlideElement>) => void;
}) {
  switch (element.type) {
    case "text":
      return (
        <div
          contentEditable
          suppressContentEditableWarning
          className="w-full h-full p-2 outline-none resize-none overflow-hidden"
          style={{
            fontSize: element.style.fontSize,
            color: element.style.color,
            backgroundColor: element.style.backgroundColor,
            fontFamily: element.style.fontFamily,
            fontWeight: element.style.bold ? "bold" : "normal",
            fontStyle: element.style.italic ? "italic" : "normal",
            textAlign: element.style.textAlign,
          }}
          onBlur={(e) => {
            onUpdate({ text: e.target.textContent || "" });
          }}
          dangerouslySetInnerHTML={{ __html: element.text }}
        />
      );

    case "image":
      return (
        <img
          src={element.src}
          alt={element.alt}
          className="w-full h-full object-cover rounded"
          draggable={false}
        />
      );

    case "shape":
      return (
        <div
          className="w-full h-full rounded"
          style={{
            backgroundColor: element.fill,
            border: `${element.strokeWidth}px solid ${element.stroke}`,
          }}
        />
      );

    case "table":
      return (
        <div className="w-full h-full overflow-auto">
          <table className="w-full h-full border-collapse">
            <tbody>
              {element.rows.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {row.map((cell, cellIndex) => (
                    <td
                      key={cellIndex}
                      contentEditable
                      suppressContentEditableWarning
                      className="border border-gray-300 p-1 min-w-[50px]"
                      style={{
                        fontSize: element.style?.fontSize,
                        backgroundColor: element.style?.backgroundColor,
                        borderColor: element.style?.borderColor,
                      }}
                      onBlur={(e) => {
                        const newRows = [...element.rows];
                        newRows[rowIndex][cellIndex] =
                          e.target.textContent || "";
                        onUpdate({ rows: newRows });
                      }}
                      dangerouslySetInnerHTML={{ __html: cell }}
                    />
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );

    default:
      return <div>Unknown element type</div>;
  }
}

// Style Panel Component
function StylePanel({
  element,
  onUpdate,
}: {
  element: SlideElement;
  onUpdate: (updates: Partial<SlideElement>) => void;
}) {
  return (
    <div className="w-64 bg-white shadow-lg p-4 space-y-4 border-l">
      <h3 className="font-bold text-gray-800">Style Panel</h3>

      {/* Text Styles */}
      {element.type === "text" && (
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Font Size
            </label>
            <input
              type="range"
              min="8"
              max="72"
              value={element.style.fontSize}
              onChange={(e) =>
                onUpdate({
                  style: {
                    ...element.style,
                    fontSize: parseInt(e.target.value),
                  },
                })
              }
              className="w-full"
            />
            <span className="text-xs text-gray-500">
              {element.style.fontSize}px
            </span>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Text Color
            </label>
            <input
              type="color"
              value={element.style.color}
              onChange={(e) =>
                onUpdate({
                  style: { ...element.style, color: e.target.value },
                })
              }
              className="w-full h-8 rounded"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Background Color
            </label>
            <input
              type="color"
              value={element.style.backgroundColor || "#ffffff"}
              onChange={(e) =>
                onUpdate({
                  style: { ...element.style, backgroundColor: e.target.value },
                })
              }
              className="w-full h-8 rounded"
            />
          </div>

          <div className="flex gap-2">
            <button
              onClick={() =>
                onUpdate({
                  style: { ...element.style, bold: !element.style.bold },
                })
              }
              className={`px-3 py-1 rounded text-sm font-bold ${
                element.style.bold
                  ? "bg-blue-500 text-white"
                  : "bg-gray-200 text-gray-700"
              }`}
            >
              B
            </button>
            <button
              onClick={() =>
                onUpdate({
                  style: { ...element.style, italic: !element.style.italic },
                })
              }
              className={`px-3 py-1 rounded text-sm italic ${
                element.style.italic
                  ? "bg-blue-500 text-white"
                  : "bg-gray-200 text-gray-700"
              }`}
            >
              I
            </button>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Text Align
            </label>
            <div className="flex gap-1">
              {(["left", "center", "right"] as const).map((align) => (
                <button
                  key={align}
                  onClick={() =>
                    onUpdate({
                      style: { ...element.style, textAlign: align },
                    })
                  }
                  className={`px-3 py-1 rounded text-sm capitalize ${
                    element.style.textAlign === align
                      ? "bg-blue-500 text-white"
                      : "bg-gray-200 text-gray-700"
                  }`}
                >
                  {align}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Shape Styles */}
      {element.type === "shape" && (
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Fill Color
            </label>
            <input
              type="color"
              value={element.fill || "#3B82F6"}
              onChange={(e) => onUpdate({ fill: e.target.value })}
              className="w-full h-8 rounded"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Border Color
            </label>
            <input
              type="color"
              value={element.stroke || "#1E40AF"}
              onChange={(e) => onUpdate({ stroke: e.target.value })}
              className="w-full h-8 rounded"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Border Width
            </label>
            <input
              type="range"
              min="0"
              max="10"
              value={element.strokeWidth || 2}
              onChange={(e) =>
                onUpdate({ strokeWidth: parseInt(e.target.value) })
              }
              className="w-full"
            />
            <span className="text-xs text-gray-500">
              {element.strokeWidth || 2}px
            </span>
          </div>
        </div>
      )}

      {/* Table Styles */}
      {element.type === "table" && (
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Font Size
            </label>
            <input
              type="range"
              min="8"
              max="24"
              value={element.style?.fontSize || 14}
              onChange={(e) =>
                onUpdate({
                  style: {
                    ...element.style,
                    fontSize: parseInt(e.target.value),
                  },
                })
              }
              className="w-full"
            />
            <span className="text-xs text-gray-500">
              {element.style?.fontSize || 14}px
            </span>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Border Color
            </label>
            <input
              type="color"
              value={element.style?.borderColor || "#000000"}
              onChange={(e) =>
                onUpdate({
                  style: { ...element.style, borderColor: e.target.value },
                })
              }
              className="w-full h-8 rounded"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Background Color
            </label>
            <input
              type="color"
              value={element.style?.backgroundColor || "#ffffff"}
              onChange={(e) =>
                onUpdate({
                  style: { ...element.style, backgroundColor: e.target.value },
                })
              }
              className="w-full h-8 rounded"
            />
          </div>
        </div>
      )}

      {/* Common Properties */}
      <div className="pt-4 border-t space-y-3">
        <h4 className="font-semibold text-gray-700">Position & Size</h4>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-xs text-gray-600">X</label>
            <input
              type="number"
              value={Math.round(element.x)}
              onChange={(e) => onUpdate({ x: parseInt(e.target.value) || 0 })}
              className="w-full px-2 py-1 border rounded text-sm"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-600">Y</label>
            <input
              type="number"
              value={Math.round(element.y)}
              onChange={(e) => onUpdate({ y: parseInt(e.target.value) || 0 })}
              className="w-full px-2 py-1 border rounded text-sm"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-600">Width</label>
            <input
              type="number"
              value={Math.round(element.width)}
              onChange={(e) =>
                onUpdate({ width: parseInt(e.target.value) || 50 })
              }
              className="w-full px-2 py-1 border rounded text-sm"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-600">Height</label>
            <input
              type="number"
              value={Math.round(element.height)}
              onChange={(e) =>
                onUpdate({ height: parseInt(e.target.value) || 50 })
              }
              className="w-full px-2 py-1 border rounded text-sm"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
