"use client";

import React, { useState } from "react";
import { nanoid } from "nanoid";
import { Type, Image, Table, Square, Download, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/Button";

// Simple types
interface SlideElement {
  id: string;
  type: "text" | "image" | "table" | "shape";
  x: number;
  y: number;
  width: number;
  height: number;
  content: string | string[][];
  style: {
    fontSize?: number;
    color?: string;
    backgroundColor?: string;
  };
}

export default function SimpleSlideEditor() {
  const [elements, setElements] = useState<SlideElement[]>([]);
  const [selectedId, setSelectedId] = useState<string | null>(null);

  // Add element functions
  const addText = () => {
    const newElement: SlideElement = {
      id: nanoid(),
      type: "text",
      x: 100,
      y: 100,
      width: 200,
      height: 50,
      content: "Nhấp đúp để chỉnh sửa",
      style: { fontSize: 16, color: "#000000" },
    };
    setElements([...elements, newElement]);
  };

  const addTable = () => {
    const newElement: SlideElement = {
      id: nanoid(),
      type: "table",
      x: 100,
      y: 200,
      width: 300,
      height: 120,
      content: [
        ["Tiêu đề 1", "Tiêu đề 2"],
        ["Dữ liệu 1", "Dữ liệu 2"],
      ],
      style: { fontSize: 14, color: "#000000" },
    };
    setElements([...elements, newElement]);
  };

  const addImage = () => {
    const newElement: SlideElement = {
      id: nanoid(),
      type: "image",
      x: 400,
      y: 100,
      width: 200,
      height: 150,
      content: "/images/placeholder-image.svg",
      style: {},
    };
    setElements([...elements, newElement]);
  };

  const addShape = () => {
    const newElement: SlideElement = {
      id: nanoid(),
      type: "shape",
      x: 300,
      y: 300,
      width: 100,
      height: 100,
      content: "",
      style: { backgroundColor: "#3b82f6" },
    };
    setElements([...elements, newElement]);
  };

  // Update element
  const updateElement = (id: string, updates: Partial<SlideElement>) => {
    setElements(elements.map(el => el.id === id ? { ...el, ...updates } : el));
  };

  // Delete element
  const deleteElement = (id: string) => {
    setElements(elements.filter(el => el.id !== id));
    setSelectedId(null);
  };

  // Export JSON
  const exportJSON = () => {
    const data = JSON.stringify({ elements }, null, 2);
    const blob = new Blob([data], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "slide.json";
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <div className="w-64 bg-white border-r p-4 space-y-4">
        <h2 className="text-lg font-calsans text-gray-900">Thêm thành phần</h2>
        
        <div className="space-y-2">
          <Button onClick={addText} className="w-full justify-start">
            <Type className="h-4 w-4 mr-2" />
            Text
          </Button>
          
          <Button onClick={addTable} className="w-full justify-start">
            <Table className="h-4 w-4 mr-2" />
            Bảng
          </Button>
          
          <Button onClick={addImage} className="w-full justify-start">
            <Image className="h-4 w-4 mr-2" />
            Hình ảnh
          </Button>
          
          <Button onClick={addShape} className="w-full justify-start">
            <Square className="h-4 w-4 mr-2" />
            Hình khối
          </Button>
        </div>

        <hr />

        <div className="space-y-2">
          <Button onClick={exportJSON} variant="outline" className="w-full justify-start">
            <Download className="h-4 w-4 mr-2" />
            Export JSON
          </Button>
          
          <Button 
            onClick={() => setElements([])} 
            variant="outline" 
            className="w-full justify-start text-red-600"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Xóa tất cả
          </Button>
        </div>

        {/* Info */}
        <div className="text-sm text-gray-600">
          <p>Elements: {elements.length}</p>
          <p>Selected: {selectedId ? "Yes" : "None"}</p>
        </div>
      </div>

      {/* Canvas */}
      <div className="flex-1 bg-gray-100 flex items-center justify-center p-8">
        <div 
          className="relative bg-white shadow-lg"
          style={{ width: 960, height: 540 }}
          onClick={() => setSelectedId(null)}
        >
          {/* Grid */}
          <div 
            className="absolute inset-0 opacity-10 pointer-events-none"
            style={{
              backgroundImage: `
                linear-gradient(to right, #000 1px, transparent 1px),
                linear-gradient(to bottom, #000 1px, transparent 1px)
              `,
              backgroundSize: "20px 20px",
            }}
          />

          {/* Elements */}
          {elements.map((element) => (
            <ElementComponent
              key={element.id}
              element={element}
              isSelected={selectedId === element.id}
              onSelect={() => setSelectedId(element.id)}
              onUpdate={(updates) => updateElement(element.id, updates)}
              onDelete={() => deleteElement(element.id)}
            />
          ))}

          {/* Empty state */}
          {elements.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center text-gray-400">
              Thêm elements từ sidebar bên trái
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Element Component
function ElementComponent({ 
  element, 
  isSelected, 
  onSelect, 
  onUpdate, 
  onDelete 
}: {
  element: SlideElement;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (updates: Partial<SlideElement>) => void;
  onDelete: () => void;
}) {
  const [isEditing, setIsEditing] = useState(false);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect();
    
    const startX = e.clientX - element.x;
    const startY = e.clientY - element.y;

    const handleMouseMove = (e: MouseEvent) => {
      onUpdate({
        x: e.clientX - startX,
        y: e.clientY - startY,
      });
    };

    const handleMouseUp = () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  const renderContent = () => {
    switch (element.type) {
      case "text":
        return isEditing ? (
          <textarea
            autoFocus
            value={element.content as string}
            onChange={(e) => onUpdate({ content: e.target.value })}
            onBlur={() => setIsEditing(false)}
            className="w-full h-full resize-none bg-transparent outline-none"
            style={{ fontSize: element.style.fontSize, color: element.style.color }}
          />
        ) : (
          <div 
            className="w-full h-full p-2"
            style={{ fontSize: element.style.fontSize, color: element.style.color }}
            onDoubleClick={() => setIsEditing(true)}
          >
            {element.content as string}
          </div>
        );

      case "table":
        const tableData = element.content as string[][];
        return (
          <table className="w-full h-full border-collapse">
            <tbody>
              {tableData.map((row, i) => (
                <tr key={i}>
                  {row.map((cell, j) => (
                    <td 
                      key={j} 
                      className="border border-gray-300 p-1 text-sm"
                      contentEditable
                      suppressContentEditableWarning
                      onBlur={(e) => {
                        const newData = [...tableData];
                        newData[i][j] = e.currentTarget.textContent || "";
                        onUpdate({ content: newData });
                      }}
                    >
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        );

      case "image":
        return (
          <img 
            src={element.content as string} 
            alt="Element" 
            className="w-full h-full object-cover"
            draggable={false}
          />
        );

      case "shape":
        return (
          <div 
            className="w-full h-full"
            style={{ backgroundColor: element.style.backgroundColor }}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div
      className={`absolute cursor-move border-2 ${
        isSelected ? "border-blue-500" : "border-transparent"
      }`}
      style={{
        left: element.x,
        top: element.y,
        width: element.width,
        height: element.height,
      }}
      onMouseDown={handleMouseDown}
    >
      {renderContent()}
      
      {isSelected && (
        <button
          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs"
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
        >
          ×
        </button>
      )}
    </div>
  );
}
