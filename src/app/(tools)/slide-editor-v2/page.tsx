"use client";

import { useState, useRef } from "react";
import { Rnd } from "react-rnd";
import { SlideElement, SlideEditorState, Slide } from "@/types/slideEditor";
import {
  Plus,
  Type,
  Image,
  Square,
  Grid3X3,
  Download,
  Upload,
  Trash2,
  Circle,
  Triangle,
  Star,
  Copy,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

export default function SlideEditorV2Page() {
  const initialSlide: Slide = {
    id: "slide-1",
    name: "Slide 1",
    elements: [],
  };

  const [state, setState] = useState<SlideEditorState>({
    slides: [initialSlide],
    currentSlideId: "slide-1",
    selectedElementId: null,
    canvasSize: { width: 800, height: 600 },
  });

  const currentSlide =
    state.slides.find((s) => s.id === state.currentSlideId) || state.slides[0];
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Slide operations
  const addSlide = () => {
    const newSlide: Slide = {
      id: `slide-${Date.now()}`,
      name: `Slide ${state.slides.length + 1}`,
      elements: [],
    };
    setState((prev) => ({
      ...prev,
      slides: [...prev.slides, newSlide],
      currentSlideId: newSlide.id,
      selectedElementId: null,
    }));
  };

  const deleteSlide = (slideId: string) => {
    if (state.slides.length <= 1) return;
    setState((prev) => {
      const newSlides = prev.slides.filter((s) => s.id !== slideId);
      const newCurrentId =
        prev.currentSlideId === slideId ? newSlides[0].id : prev.currentSlideId;
      return {
        ...prev,
        slides: newSlides,
        currentSlideId: newCurrentId,
        selectedElementId: null,
      };
    });
  };

  const switchSlide = (slideId: string) => {
    setState((prev) => ({
      ...prev,
      currentSlideId: slideId,
      selectedElementId: null,
    }));
  };

  // Element operations
  const addElement = (type: SlideElement["type"], shapeType?: string) => {
    const newElement: SlideElement = {
      id: `${type}-${Date.now()}`,
      type,
      x: 50,
      y: 50,
      width: type === "text" ? 200 : type === "table" ? 300 : 150,
      height: type === "text" ? 50 : type === "table" ? 200 : 100,
      ...(type === "text" && {
        text: "Click to edit text",
        style: {
          fontSize: 16,
          color: "#000000",
          fontFamily: "Arial",
          textAlign: "left" as const,
        },
      }),
      ...(type === "image" && {
        src: "https://via.placeholder.com/150x100?text=Image",
        alt: "Placeholder image",
      }),
      ...(type === "shape" && {
        shapeType: shapeType || "rectangle",
        svgPath: "",
        fill: "#3B82F6",
        stroke: "#1E40AF",
        strokeWidth: 2,
      }),
      ...(type === "table" && {
        rows: [
          ["Header 1", "Header 2", "Header 3"],
          ["Row 1 Col 1", "Row 1 Col 2", "Row 1 Col 3"],
          ["Row 2 Col 1", "Row 2 Col 2", "Row 2 Col 3"],
        ],
        style: {
          borderColor: "#000000",
          backgroundColor: "#FFFFFF",
          fontSize: 14,
        },
      }),
    } as SlideElement;

    setState((prev) => ({
      ...prev,
      slides: prev.slides.map((slide) =>
        slide.id === prev.currentSlideId
          ? { ...slide, elements: [...slide.elements, newElement] }
          : slide
      ),
      selectedElementId: newElement.id,
    }));
  };

  const updateElement = (id: string, updates: Partial<SlideElement>) => {
    setState((prev) => ({
      ...prev,
      slides: prev.slides.map((slide) =>
        slide.id === prev.currentSlideId
          ? {
              ...slide,
              elements: slide.elements.map((el) =>
                el.id === id ? { ...el, ...updates } : el
              ),
            }
          : slide
      ),
    }));
  };

  const deleteElement = (id: string) => {
    setState((prev) => ({
      ...prev,
      slides: prev.slides.map((slide) =>
        slide.id === prev.currentSlideId
          ? { ...slide, elements: slide.elements.filter((el) => el.id !== id) }
          : slide
      ),
      selectedElementId:
        prev.selectedElementId === id ? null : prev.selectedElementId,
    }));
  };

  // Image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        addElement("image");
        // Update the last added image with actual file
        setTimeout(() => {
          const lastElement =
            currentSlide.elements[currentSlide.elements.length - 1];
          if (lastElement && lastElement.type === "image") {
            updateElement(lastElement.id, {
              src: e.target?.result as string,
              alt: file.name,
            });
          }
        }, 100);
      };
      reader.readAsDataURL(file);
    }
  };

  // Export
  const exportToJSON = () => {
    const exportData = { canvasSize: state.canvasSize, slides: state.slides };
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "slide-design.json";
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="h-screen flex bg-gray-100">
      {/* Left Sidebar - Tools */}
      <div className="w-64 bg-white shadow-lg flex flex-col">
        {/* Tools Section */}
        <div className="p-4 border-b">
          <h2 className="text-lg font-bold text-gray-800 mb-4">Add Elements</h2>

          <div className="space-y-2">
            <button
              onClick={() => addElement("text")}
              className="w-full flex items-center gap-2 p-3 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              <Type size={16} />
              Text
            </button>

            <button
              onClick={() => fileInputRef.current?.click()}
              className="w-full flex items-center gap-2 p-3 bg-green-500 text-white rounded hover:bg-green-600"
            >
              <Image size={16} />
              Image
            </button>

            {/* Shape Dropdown */}
            <div className="space-y-1">
              <button
                onClick={() => addElement("shape", "rectangle")}
                className="w-full flex items-center gap-2 p-2 bg-purple-500 text-white rounded hover:bg-purple-600 text-sm"
              >
                <Square size={14} />
                Rectangle
              </button>
              <button
                onClick={() => addElement("shape", "circle")}
                className="w-full flex items-center gap-2 p-2 bg-purple-500 text-white rounded hover:bg-purple-600 text-sm"
              >
                <Circle size={14} />
                Circle
              </button>
              <button
                onClick={() => addElement("shape", "triangle")}
                className="w-full flex items-center gap-2 p-2 bg-purple-500 text-white rounded hover:bg-purple-600 text-sm"
              >
                <Triangle size={14} />
                Triangle
              </button>
            </div>

            <button
              onClick={() => addElement("table")}
              className="w-full flex items-center gap-2 p-3 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              <Grid3X3 size={16} />
              Table
            </button>
          </div>
        </div>

        {/* Slides Section */}
        <div className="flex-1 p-4 overflow-y-auto">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-700">Slides</h3>
            <button
              onClick={addSlide}
              className="p-1 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              <Plus size={16} />
            </button>
          </div>

          <div className="space-y-2">
            {state.slides.map((slide, index) => (
              <div
                key={slide.id}
                className={`relative p-3 rounded cursor-pointer border-2 ${
                  state.currentSlideId === slide.id
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 bg-gray-50 hover:bg-gray-100"
                }`}
                onClick={() => switchSlide(slide.id)}
              >
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{slide.name}</span>
                  {state.slides.length > 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteSlide(slide.id);
                      }}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 size={14} />
                    </button>
                  )}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {slide.elements.length} elements
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Export */}
        <div className="p-4 border-t">
          <button
            onClick={exportToJSON}
            className="w-full flex items-center gap-2 p-3 bg-gray-700 text-white rounded hover:bg-gray-800"
          >
            <Download size={16} />
            Export JSON
          </button>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />

      {/* Canvas Area */}
      <div className="flex-1 flex flex-col">
        {/* Canvas Header */}
        <div className="bg-white shadow-sm p-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold text-gray-800">Slide Editor</h1>
            <span className="text-sm text-gray-500">
              {currentSlide.name} ({currentSlide.elements.length} elements)
            </span>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => {
                const currentIndex = state.slides.findIndex(
                  (s) => s.id === state.currentSlideId
                );
                if (currentIndex > 0) {
                  switchSlide(state.slides[currentIndex - 1].id);
                }
              }}
              disabled={
                state.slides.findIndex((s) => s.id === state.currentSlideId) ===
                0
              }
              className="p-2 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
            >
              <ChevronLeft size={16} />
            </button>

            <span className="text-sm text-gray-600">
              {state.slides.findIndex((s) => s.id === state.currentSlideId) + 1}{" "}
              / {state.slides.length}
            </span>

            <button
              onClick={() => {
                const currentIndex = state.slides.findIndex(
                  (s) => s.id === state.currentSlideId
                );
                if (currentIndex < state.slides.length - 1) {
                  switchSlide(state.slides[currentIndex + 1].id);
                }
              }}
              disabled={
                state.slides.findIndex((s) => s.id === state.currentSlideId) ===
                state.slides.length - 1
              }
              className="p-2 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
            >
              <ChevronRight size={16} />
            </button>
          </div>
        </div>

        {/* Canvas */}
        <div className="flex-1 p-4 overflow-auto">
          <div
            className="relative bg-white shadow-lg mx-auto border"
            style={{
              width: state.canvasSize.width,
              height: state.canvasSize.height,
            }}
          >
            {currentSlide.elements.map((element) => (
              <ElementRenderer
                key={element.id}
                element={element}
                isSelected={state.selectedElementId === element.id}
                onUpdate={(updates) => updateElement(element.id, updates)}
                onSelect={() =>
                  setState((prev) => ({
                    ...prev,
                    selectedElementId: element.id,
                  }))
                }
                onDelete={() => deleteElement(element.id)}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Right Sidebar - Style Panel */}
      {state.selectedElementId && (
        <StylePanel
          element={
            currentSlide.elements.find(
              (el) => el.id === state.selectedElementId
            )!
          }
          onUpdate={(updates) =>
            updateElement(state.selectedElementId!, updates)
          }
          onDelete={() => deleteElement(state.selectedElementId!)}
        />
      )}
    </div>
  );
}

// Element Renderer Component
function ElementRenderer({
  element,
  isSelected,
  onUpdate,
  onSelect,
  onDelete,
}: {
  element: SlideElement;
  isSelected: boolean;
  onUpdate: (updates: Partial<SlideElement>) => void;
  onSelect: () => void;
  onDelete: () => void;
}) {
  return (
    <Rnd
      size={{ width: element.width, height: element.height }}
      position={{ x: element.x, y: element.y }}
      onDragStop={(e, d) => {
        onUpdate({ x: d.x, y: d.y });
      }}
      onResizeStop={(e, direction, ref, delta, position) => {
        onUpdate({
          width: ref.offsetWidth,
          height: ref.offsetHeight,
          x: position.x,
          y: position.y,
        });
      }}
      onClick={onSelect}
      className={`group ${isSelected ? "ring-2 ring-blue-500" : ""}`}
    >
      <div className="relative w-full h-full">
        <ElementContent element={element} onUpdate={onUpdate} />

        {/* Delete button - appears on hover */}
        {isSelected && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 z-10"
          >
            <Trash2 size={12} />
          </button>
        )}
      </div>
    </Rnd>
  );
}

// Element Content Component
function ElementContent({
  element,
  onUpdate,
}: {
  element: SlideElement;
  onUpdate: (updates: Partial<SlideElement>) => void;
}) {
  switch (element.type) {
    case "text":
      return (
        <div
          contentEditable
          suppressContentEditableWarning
          className="w-full h-full p-2 outline-none resize-none overflow-hidden border border-transparent hover:border-gray-300 focus:border-blue-500"
          style={{
            fontSize: element.style.fontSize,
            color: element.style.color,
            backgroundColor: element.style.backgroundColor,
            fontFamily: element.style.fontFamily,
            fontWeight: element.style.bold ? "bold" : "normal",
            fontStyle: element.style.italic ? "italic" : "normal",
            textAlign: element.style.textAlign,
          }}
          onBlur={(e) => {
            onUpdate({ text: e.target.textContent || "" });
          }}
          dangerouslySetInnerHTML={{ __html: element.text }}
        />
      );

    case "image":
      return (
        <img
          src={element.src}
          alt={element.alt}
          className="w-full h-full object-cover rounded border"
          draggable={false}
        />
      );

    case "shape":
      const renderShape = () => {
        switch (element.shapeType) {
          case "circle":
            return (
              <div
                className="w-full h-full rounded-full"
                style={{
                  backgroundColor: element.fill,
                  border: `${element.strokeWidth}px solid ${element.stroke}`,
                }}
              />
            );
          case "triangle":
            return (
              <div
                className="w-full h-full"
                style={{
                  clipPath: "polygon(50% 0%, 0% 100%, 100% 100%)",
                  backgroundColor: element.fill,
                  border: `${element.strokeWidth}px solid ${element.stroke}`,
                }}
              />
            );
          default: // rectangle
            return (
              <div
                className="w-full h-full rounded"
                style={{
                  backgroundColor: element.fill,
                  border: `${element.strokeWidth}px solid ${element.stroke}`,
                }}
              />
            );
        }
      };
      return renderShape();

    case "table":
      return (
        <div className="w-full h-full overflow-auto border">
          <table className="w-full h-full border-collapse">
            <tbody>
              {element.rows.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {row.map((cell, cellIndex) => (
                    <td
                      key={cellIndex}
                      contentEditable
                      suppressContentEditableWarning
                      className="border p-1 min-w-[50px] hover:bg-gray-50 focus:bg-blue-50 outline-none"
                      style={{
                        fontSize: element.style?.fontSize,
                        backgroundColor: element.style?.backgroundColor,
                        borderColor: element.style?.borderColor,
                      }}
                      onBlur={(e) => {
                        const newRows = [...element.rows];
                        newRows[rowIndex][cellIndex] =
                          e.target.textContent || "";
                        onUpdate({ rows: newRows });
                      }}
                      dangerouslySetInnerHTML={{ __html: cell }}
                    />
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );

    default:
      return <div>Unknown element type</div>;
  }
}

// Style Panel Component
function StylePanel({
  element,
  onUpdate,
  onDelete,
}: {
  element: SlideElement;
  onUpdate: (updates: Partial<SlideElement>) => void;
  onDelete: () => void;
}) {
  const fontOptions = [
    "Arial",
    "Helvetica",
    "Times New Roman",
    "Georgia",
    "Verdana",
    "Courier New",
  ];

  return (
    <div className="w-80 bg-white shadow-lg border-l flex flex-col max-h-screen">
      {/* Header */}
      <div className="p-4 border-b flex items-center justify-between">
        <h3 className="font-bold text-gray-800">Style Panel</h3>
        <button
          onClick={onDelete}
          className="p-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          <Trash2 size={16} />
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 space-y-4 overflow-y-auto">
        {/* Text Styles */}
        {element.type === "text" && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Font Family
              </label>
              <select
                value={element.style.fontFamily || "Arial"}
                onChange={(e) =>
                  onUpdate({
                    style: { ...element.style, fontFamily: e.target.value },
                  })
                }
                className="w-full p-2 border rounded"
              >
                {fontOptions.map((font) => (
                  <option key={font} value={font}>
                    {font}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Font Size: {element.style.fontSize}px
              </label>
              <input
                type="range"
                min="8"
                max="72"
                value={element.style.fontSize}
                onChange={(e) =>
                  onUpdate({
                    style: {
                      ...element.style,
                      fontSize: parseInt(e.target.value),
                    },
                  })
                }
                className="w-full"
              />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Text Color
                </label>
                <input
                  type="color"
                  value={element.style.color}
                  onChange={(e) =>
                    onUpdate({
                      style: { ...element.style, color: e.target.value },
                    })
                  }
                  className="w-full h-10 rounded border"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Background
                </label>
                <input
                  type="color"
                  value={element.style.backgroundColor || "#ffffff"}
                  onChange={(e) =>
                    onUpdate({
                      style: {
                        ...element.style,
                        backgroundColor: e.target.value,
                      },
                    })
                  }
                  className="w-full h-10 rounded border"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <button
                onClick={() =>
                  onUpdate({
                    style: { ...element.style, bold: !element.style.bold },
                  })
                }
                className={`flex-1 py-2 px-3 rounded font-bold ${
                  element.style.bold
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-700"
                }`}
              >
                Bold
              </button>
              <button
                onClick={() =>
                  onUpdate({
                    style: { ...element.style, italic: !element.style.italic },
                  })
                }
                className={`flex-1 py-2 px-3 rounded italic ${
                  element.style.italic
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-700"
                }`}
              >
                Italic
              </button>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Text Alignment
              </label>
              <div className="grid grid-cols-3 gap-1">
                {(["left", "center", "right"] as const).map((align) => (
                  <button
                    key={align}
                    onClick={() =>
                      onUpdate({
                        style: { ...element.style, textAlign: align },
                      })
                    }
                    className={`py-2 px-3 rounded text-sm capitalize ${
                      element.style.textAlign === align
                        ? "bg-blue-500 text-white"
                        : "bg-gray-200 text-gray-700"
                    }`}
                  >
                    {align}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Shape Styles */}
        {element.type === "shape" && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Shape Type
              </label>
              <select
                value={element.shapeType}
                onChange={(e) => onUpdate({ shapeType: e.target.value })}
                className="w-full p-2 border rounded"
              >
                <option value="rectangle">Rectangle</option>
                <option value="circle">Circle</option>
                <option value="triangle">Triangle</option>
              </select>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Fill Color
                </label>
                <input
                  type="color"
                  value={element.fill || "#3B82F6"}
                  onChange={(e) => onUpdate({ fill: e.target.value })}
                  className="w-full h-10 rounded border"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Border Color
                </label>
                <input
                  type="color"
                  value={element.stroke || "#1E40AF"}
                  onChange={(e) => onUpdate({ stroke: e.target.value })}
                  className="w-full h-10 rounded border"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Border Width: {element.strokeWidth || 2}px
              </label>
              <input
                type="range"
                min="0"
                max="10"
                value={element.strokeWidth || 2}
                onChange={(e) =>
                  onUpdate({ strokeWidth: parseInt(e.target.value) })
                }
                className="w-full"
              />
            </div>
          </div>
        )}

        {/* Table Styles */}
        {element.type === "table" && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Font Size: {element.style?.fontSize || 14}px
              </label>
              <input
                type="range"
                min="8"
                max="24"
                value={element.style?.fontSize || 14}
                onChange={(e) =>
                  onUpdate({
                    style: {
                      ...element.style,
                      fontSize: parseInt(e.target.value),
                    },
                  })
                }
                className="w-full"
              />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Border Color
                </label>
                <input
                  type="color"
                  value={element.style?.borderColor || "#000000"}
                  onChange={(e) =>
                    onUpdate({
                      style: { ...element.style, borderColor: e.target.value },
                    })
                  }
                  className="w-full h-10 rounded border"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Background
                </label>
                <input
                  type="color"
                  value={element.style?.backgroundColor || "#ffffff"}
                  onChange={(e) =>
                    onUpdate({
                      style: {
                        ...element.style,
                        backgroundColor: e.target.value,
                      },
                    })
                  }
                  className="w-full h-10 rounded border"
                />
              </div>
            </div>
          </div>
        )}

        {/* Common Properties */}
        <div className="pt-4 border-t space-y-4">
          <h4 className="font-semibold text-gray-700">Position & Size</h4>

          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-xs text-gray-600 mb-1">
                X Position
              </label>
              <input
                type="number"
                value={Math.round(element.x)}
                onChange={(e) => onUpdate({ x: parseInt(e.target.value) || 0 })}
                className="w-full px-2 py-1 border rounded text-sm"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">
                Y Position
              </label>
              <input
                type="number"
                value={Math.round(element.y)}
                onChange={(e) => onUpdate({ y: parseInt(e.target.value) || 0 })}
                className="w-full px-2 py-1 border rounded text-sm"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">Width</label>
              <input
                type="number"
                value={Math.round(element.width)}
                onChange={(e) =>
                  onUpdate({ width: parseInt(e.target.value) || 50 })
                }
                className="w-full px-2 py-1 border rounded text-sm"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">Height</label>
              <input
                type="number"
                value={Math.round(element.height)}
                onChange={(e) =>
                  onUpdate({ height: parseInt(e.target.value) || 50 })
                }
                className="w-full px-2 py-1 border rounded text-sm"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
