"use client";

import React, { useState } from "react";
import PptxUploadSimple, {
  PptxSlide,
} from "@/components/molecules/pptx-upload-simple";
import ShapeRenderer from "@/components/atoms/ShapeRenderer";
import { Button } from "@/components/ui/Button";
import {
  Download,
  Copy,
  FileText,
  Image as ImageIcon,
  Square,
  Eye,
  Code,
} from "lucide-react";
import { toast } from "sonner";

export default function PptxAllPage() {
  const [slides, setSlides] = useState<PptxSlide[]>([]);
  const [activeTab, setActiveTab] = useState<
    "slides" | "images" | "shapes" | "json" | "debug"
  >("slides");

  const handleSlidesChange = (newSlides: PptxSlide[]) => {
    setSlides(newSlides);
    console.log("PPTX Data:", newSlides);
  };

  const handleError = (error: string) => {
    console.error("PPTX Import Error:", error);
  };

  // Extract all data
  const allImages = slides.flatMap((slide, slideIndex) =>
    slide.images.map((image, imageIndex) => ({
      ...image,
      slideNumber: slide.slideNumber,
      imageIndex,
      id: `slide-${slideIndex}-image-${imageIndex}`,
    }))
  );

  const allShapes = slides.flatMap((slide, slideIndex) =>
    slide.shapes.map((shape, shapeIndex) => ({
      ...shape,
      slideNumber: slide.slideNumber,
      shapeIndex,
      id: `slide-${slideIndex}-shape-${shapeIndex}`,
    }))
  );

  const allTexts = slides.flatMap((slide, slideIndex) =>
    slide.texts.map((text, textIndex) => ({
      ...text,
      slideNumber: slide.slideNumber,
      textIndex,
      id: `slide-${slideIndex}-text-${textIndex}`,
    }))
  );

  // Export functions
  const exportJson = () => {
    const data = JSON.stringify(slides, null, 2);
    const blob = new Blob([data], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "pptx-complete.json";
    a.click();
    URL.revokeObjectURL(url);
  };

  const copyToClipboard = async (data: any) => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(data, null, 2));
      toast.success("Đã copy vào clipboard!");
    } catch (err) {
      toast.error("Không thể copy");
    }
  };

  const downloadImage = (image: any) => {
    if (image.src.startsWith("data:")) {
      const link = document.createElement("a");
      link.href = image.src;
      link.download = image.name || `image-${image.id}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const getShapeTypeLabel = (type: string): string => {
    const typeLabels: { [key: string]: string } = {
      rect: "Rectangle",
      roundRect: "Rounded Rectangle",
      ellipse: "Ellipse/Circle",
      triangle: "Triangle",
      diamond: "Diamond",
      pentagon: "Pentagon",
      hexagon: "Hexagon",
      star5: "5-Point Star",
      star6: "6-Point Star",
      rightArrow: "Right Arrow",
      leftArrow: "Left Arrow",
      upArrow: "Up Arrow",
      downArrow: "Down Arrow",
      line: "Line",
      plus: "Plus",
      heart: "Heart",
      smileyFace: "Smiley Face",
      flowChartProcess: "Process Box",
      flowChartDecision: "Decision Diamond",
      custom: "Custom Shape",
    };
    return typeLabels[type] || type;
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-calsans text-gray-900 mb-2">
            PPTX Complete Extractor
          </h1>
          <p className="text-gray-600 font-questrial">
            Upload file PPTX để extract tất cả: text, images, shapes và JSON
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Upload Section */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-calsans text-gray-900 mb-4">
                📤 Upload PPTX
              </h2>
              <PptxUploadSimple
                onSlidesChange={handleSlidesChange}
                onError={handleError}
                maxFileSize={50}
                showFileInfo={true}
              />
            </div>

            {/* Statistics */}
            {slides.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border p-6 mt-6">
                <h3 className="text-lg font-calsans text-gray-900 mb-4">
                  📊 Thống kê
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Slides:</span>
                    <span className="font-medium">{slides.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Texts:</span>
                    <span className="font-medium">{allTexts.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Images:</span>
                    <span className="font-medium">{allImages.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Shapes:</span>
                    <span className="font-medium">{allShapes.length}</span>
                  </div>
                </div>

                <div className="space-y-2 mt-4">
                  <Button
                    onClick={exportJson}
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download JSON
                  </Button>

                  <Button
                    onClick={() => copyToClipboard(slides)}
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy All Data
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {slides.length > 0 ? (
              <div className="bg-white rounded-lg shadow-sm border">
                {/* Tabs */}
                <div className="border-b border-gray-200">
                  <nav className="flex space-x-8 px-6">
                    {[
                      {
                        id: "slides",
                        label: "Slides Overview",
                        icon: Eye,
                        count: slides.length,
                      },
                      {
                        id: "images",
                        label: "Images",
                        icon: ImageIcon,
                        count: allImages.length,
                      },
                      {
                        id: "shapes",
                        label: "Shapes",
                        icon: Square,
                        count: allShapes.length,
                      },
                      {
                        id: "json",
                        label: "JSON Data",
                        icon: Code,
                        count: null,
                      },
                      {
                        id: "debug",
                        label: "Debug Info",
                        icon: FileText,
                        count: null,
                      },
                    ].map((tab) => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id as any)}
                        className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                          activeTab === tab.id
                            ? "border-blue-500 text-blue-600"
                            : "border-transparent text-gray-500 hover:text-gray-700"
                        }`}
                      >
                        <tab.icon className="h-4 w-4" />
                        {tab.label}
                        {tab.count !== null && (
                          <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                            {tab.count}
                          </span>
                        )}
                      </button>
                    ))}
                  </nav>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                  {activeTab === "slides" && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-calsans text-gray-900">
                        📋 Slides Overview
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {slides.map((slide, index) => (
                          <div
                            key={index}
                            className="border rounded-lg p-4 bg-gray-50"
                          >
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="font-medium font-calsans text-gray-800">
                                Slide {slide.slideNumber}
                              </h4>
                              <div className="flex gap-1 text-xs">
                                {slide.texts.length > 0 && (
                                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                    {slide.texts.length}T
                                  </span>
                                )}
                                {slide.images.length > 0 && (
                                  <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">
                                    {slide.images.length}I
                                  </span>
                                )}
                                {slide.shapes.length > 0 && (
                                  <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded">
                                    {slide.shapes.length}S
                                  </span>
                                )}
                              </div>
                            </div>

                            {slide.texts.length > 0 && (
                              <div className="space-y-1">
                                {slide.texts.slice(0, 2).map((text, i) => (
                                  <p
                                    key={i}
                                    className="text-xs font-questrial text-gray-600"
                                  >
                                    •{" "}
                                    {text.text.length > 60
                                      ? text.text.substring(0, 60) + "..."
                                      : text.text}
                                  </p>
                                ))}
                                {slide.texts.length > 2 && (
                                  <p className="text-xs text-gray-500 italic">
                                    ... và {slide.texts.length - 2} text khác
                                  </p>
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {activeTab === "images" && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-calsans text-gray-900">
                          🖼️ Extracted Images
                        </h3>
                        <Button
                          onClick={() => copyToClipboard(allImages)}
                          variant="outline"
                          size="sm"
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Images Data
                        </Button>
                      </div>

                      {allImages.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {allImages.map((image, index) => (
                            <div
                              key={image.id}
                              className="border rounded-lg overflow-hidden bg-gray-50"
                            >
                              <div className="aspect-video bg-gray-100 flex items-center justify-center">
                                {image.src.startsWith("data:") ? (
                                  <img
                                    src={image.src}
                                    alt={image.name}
                                    className="max-w-full max-h-full object-contain"
                                  />
                                ) : (
                                  <div className="text-center text-gray-400">
                                    <ImageIcon className="h-8 w-8 mx-auto mb-2" />
                                    <p className="text-xs">Placeholder</p>
                                  </div>
                                )}
                              </div>
                              <div className="p-3">
                                <h4 className="font-medium text-sm text-gray-900 mb-1">
                                  {image.name || `Image ${index + 1}`}
                                </h4>
                                <p className="text-xs text-gray-500 mb-2">
                                  Slide {image.slideNumber} • {image.width}×
                                  {image.height}
                                </p>
                                {image.src.startsWith("data:") && (
                                  <Button
                                    onClick={() => downloadImage(image)}
                                    size="sm"
                                    variant="outline"
                                    className="w-full text-xs"
                                  >
                                    <Download className="h-3 w-3 mr-1" />
                                    Download
                                  </Button>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <ImageIcon className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                          <p className="text-gray-500">
                            Không có images nào được tìm thấy
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {activeTab === "shapes" && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-calsans text-gray-900">
                          🔷 Extracted Shapes
                        </h3>
                        <Button
                          onClick={() => copyToClipboard(allShapes)}
                          variant="outline"
                          size="sm"
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Shapes Data
                        </Button>
                      </div>

                      {allShapes.length > 0 ? (
                        <div className="space-y-4">
                          {allShapes.map((shape) => (
                            <div
                              key={shape.id}
                              className="border rounded-lg p-4 bg-gray-50"
                            >
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div
                                  className="flex items-center justify-center bg-white border rounded p-4"
                                  style={{ minHeight: "120px" }}
                                >
                                  <ShapeRenderer
                                    shape={shape}
                                    maxWidth={100}
                                    maxHeight={80}
                                    showText={false}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <h4 className="font-medium text-gray-900">
                                    {getShapeTypeLabel(shape.type)}
                                  </h4>
                                  <div className="grid grid-cols-2 gap-2 text-xs">
                                    <div>
                                      <span className="text-gray-500">
                                        Slide:
                                      </span>
                                      <span className="ml-1 font-medium">
                                        {shape.slideNumber}
                                      </span>
                                    </div>
                                    <div>
                                      <span className="text-gray-500">
                                        Type:
                                      </span>
                                      <span className="ml-1 font-medium">
                                        {shape.type}
                                      </span>
                                    </div>
                                    {shape.shapeId && (
                                      <div>
                                        <span className="text-gray-500">ID:</span>
                                        <span className="ml-1 font-medium text-xs">
                                          {shape.shapeId}
                                        </span>
                                      </div>
                                    )}
                                    <div>
                                      <span className="text-gray-500">
                                        Position:
                                      </span>
                                      <span className="ml-1 font-medium">
                                        {shape.x}, {shape.y}
                                      </span>
                                    </div>
                                    <div>
                                      <span className="text-gray-500">
                                        Size:
                                      </span>
                                      <span className="ml-1 font-medium">
                                        {shape.width}×{shape.height}
                                      </span>
                                    </div>
                                    <div>
                                      <span className="text-gray-500">
                                        Fill:
                                      </span>
                                      <div className="inline-flex items-center ml-1">
                                        <div
                                          className="w-3 h-3 border border-gray-300 rounded mr-1"
                                          style={{
                                            backgroundColor: shape.fill,
                                          }}
                                        ></div>
                                        <span className="font-medium text-xs">
                                          {shape.fill}
                                        </span>
                                      </div>
                                    </div>
                                    {shape.svgPath && (
                                      <div className="col-span-2">
                                        <span className="text-gray-500">
                                          SVG:
                                        </span>
                                        <span className="ml-1 font-medium text-xs">
                                          {shape.svgPath.substring(0, 50)}...
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <Square className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                          <p className="text-gray-500">
                            Không có shapes nào được tìm thấy
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {activeTab === "json" && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-calsans text-gray-900">
                          📄 JSON Data
                        </h3>
                        <div className="flex gap-2">
                          <Button
                            onClick={() => copyToClipboard(slides)}
                            variant="outline"
                            size="sm"
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Copy JSON
                          </Button>
                          <Button
                            onClick={exportJson}
                            variant="outline"
                            size="sm"
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </Button>
                        </div>
                      </div>

                      <div className="bg-gray-50 p-4 rounded-lg">
                        <pre className="text-xs overflow-auto max-h-96">
                          <code>{JSON.stringify(slides, null, 2)}</code>
                        </pre>
                      </div>
                    </div>
                  )}

                  {activeTab === "debug" && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-calsans text-gray-900">
                        🔍 Debug Information
                      </h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-medium text-gray-800 mb-2">
                            Parser Statistics
                          </h4>
                          <div className="text-sm space-y-1">
                            <p>
                              Total slides processed:{" "}
                              <span className="font-medium">
                                {slides.length}
                              </span>
                            </p>
                            <p>
                              Total elements found:{" "}
                              <span className="font-medium">
                                {allTexts.length +
                                  allImages.length +
                                  allShapes.length}
                              </span>
                            </p>
                            <p>
                              Text elements:{" "}
                              <span className="font-medium">
                                {allTexts.length}
                              </span>
                            </p>
                            <p>
                              Image elements:{" "}
                              <span className="font-medium">
                                {allImages.length}
                              </span>
                            </p>
                            <p>
                              Shape elements:{" "}
                              <span className="font-medium">
                                {allShapes.length}
                              </span>
                            </p>
                          </div>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-medium text-gray-800 mb-2">
                            Parser Features
                          </h4>
                          <div className="text-sm space-y-1">
                            <p>✅ Regular shapes (p:sp)</p>
                            <p>✅ Grouped shapes (p:grpSp)</p>
                            <p>✅ Connection shapes (p:cxnSp)</p>
                            <p>✅ Tables (p:graphicFrame)</p>
                            <p>✅ Pictures (p:pic)</p>
                            <p>✅ Position extraction (EMU → pixels)</p>
                          </div>
                        </div>
                      </div>

                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h4 className="font-medium text-yellow-800 mb-2">
                          💡 Debugging Tips
                        </h4>
                        <div className="text-sm text-yellow-700 space-y-1">
                          <p>
                            • Check browser console for detailed parsing logs
                          </p>
                          <p>• Each element type is logged during processing</p>
                          <p>
                            • Position coordinates are converted from EMU units
                          </p>
                          <p>• Grouped shapes are processed recursively</p>
                          <p>
                            • Tables are detected and marked as special text
                            elements
                          </p>
                        </div>
                      </div>

                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-800 mb-2">
                          Element Details by Slide
                        </h4>
                        <div className="space-y-2 max-h-64 overflow-y-auto">
                          {slides.map((slide, slideIndex) => (
                            <div
                              key={slideIndex}
                              className="border rounded p-2 bg-white"
                            >
                              <div className="font-medium text-sm">
                                Slide {slide.slideNumber}
                              </div>
                              <div className="text-xs text-gray-600 grid grid-cols-3 gap-2">
                                <span>Texts: {slide.texts.length}</span>
                                <span>Images: {slide.images.length}</span>
                                <span>Shapes: {slide.shapes.length}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm border p-12 text-center">
                <FileText className="h-16 w-16 mx-auto text-gray-300 mb-4" />
                <h3 className="text-lg font-calsans text-gray-900 mb-2">
                  Chưa có dữ liệu
                </h3>
                <p className="text-gray-500 font-questrial">
                  Upload file PPTX để bắt đầu extract dữ liệu
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-calsans text-blue-900 mb-3">
            📋 Tính năng hoàn chỉnh
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm font-questrial text-blue-800">
            <div>
              <p className="mb-2">
                <strong>Text Extraction:</strong>
              </p>
              <ul className="space-y-1">
                <li>• Parse text với font, size, color</li>
                <li>• Vị trí chính xác trên slide</li>
                <li>• Hỗ trợ multiple text boxes</li>
              </ul>
            </div>
            <div>
              <p className="mb-2">
                <strong>Image Extraction:</strong>
              </p>
              <ul className="space-y-1">
                <li>• Extract ảnh thật từ ppt/media/</li>
                <li>• Convert sang base64</li>
                <li>• Download từng ảnh riêng lẻ</li>
              </ul>
            </div>
            <div>
              <p className="mb-2">
                <strong>Shape Extraction:</strong>
              </p>
              <ul className="space-y-1">
                <li>• Parse position, size chính xác</li>
                <li>• Fill colors và borders</li>
                <li>• Hỗ trợ nhiều shape types</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
