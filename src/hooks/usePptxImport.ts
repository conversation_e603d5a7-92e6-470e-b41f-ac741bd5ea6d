"use client";

import { useState, useCallback } from "react";
import { PptxSlide } from "@/lib/pptxParser";

export interface PptxImportState {
  slides: PptxSlide[];
  isLoading: boolean;
  error: string | null;
  fileName: string | null;
  fileSize: number | null;
}

export interface PptxImportActions {
  setSlides: (slides: PptxSlide[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setFileInfo: (fileName: string, fileSize: number) => void;
  reset: () => void;
  exportToJson: () => string;
  downloadJson: (filename?: string) => void;
  getSlideText: (slideNumber: number) => string[];
  getAllText: () => string;
  getStatistics: () => {
    totalSlides: number;
    totalTexts: number;
    totalImages: number;
    totalShapes: number;
  };
}

const initialState: PptxImportState = {
  slides: [],
  isLoading: false,
  error: null,
  fileName: null,
  fileSize: null,
};

export function usePptxImport(): PptxImportState & PptxImportActions {
  const [state, setState] = useState<PptxImportState>(initialState);

  const setSlides = useCallback((slides: PptxSlide[]) => {
    setState((prev) => ({ ...prev, slides, error: null }));
  }, []);

  const setLoading = useCallback((isLoading: boolean) => {
    setState((prev) => ({ ...prev, isLoading }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState((prev) => ({ ...prev, error }));
  }, []);

  const setFileInfo = useCallback((fileName: string, fileSize: number) => {
    setState((prev) => ({ ...prev, fileName, fileSize }));
  }, []);

  const reset = useCallback(() => {
    setState(initialState);
  }, []);

  const exportToJson = useCallback((): string => {
    return JSON.stringify(state.slides, null, 2);
  }, [state.slides]);

  const downloadJson = useCallback(
    (filename?: string) => {
      const jsonData = exportToJson();
      const blob = new Blob([jsonData], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download =
        filename ||
        `${state.fileName?.replace(".pptx", "") || "pptx-slides"}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    },
    [exportToJson, state.fileName]
  );

  const getSlideText = useCallback(
    (slideNumber: number): string[] => {
      const slide = state.slides.find((s) => s.slideNumber === slideNumber);
      return slide?.texts.map((t) => t.text) || [];
    },
    [state.slides]
  );

  const getAllText = useCallback((): string => {
    return state.slides
      .map((slide) => slide.texts.map((t) => t.text).join(" "))
      .filter((text) => text.length > 0)
      .join("\n\n");
  }, [state.slides]);

  const getStatistics = useCallback(() => {
    return {
      totalSlides: state.slides.length,
      totalTexts: state.slides.reduce(
        (total, slide) => total + slide.texts.length,
        0
      ),
      totalImages: state.slides.reduce(
        (total, slide) => total + slide.images.length,
        0
      ),
      totalShapes: state.slides.reduce(
        (total, slide) => total + slide.shapes.length,
        0
      ),
    };
  }, [state.slides]);

  return {
    ...state,
    setSlides,
    setLoading,
    setError,
    setFileInfo,
    reset,
    exportToJson,
    downloadJson,
    getSlideText,
    getAllText,
    getStatistics,
  };
}
