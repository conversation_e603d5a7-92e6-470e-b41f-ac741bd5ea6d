"use client";

import React, { useState, useRef } from "react";
import { Upload, X, FileText, AlertCircle, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/Button";
import Image from "next/image";
import { toast } from "sonner";

// Import pptx-parser
// @ts-ignore - pptx-parser doesn't have TypeScript definitions
import { parsePptx } from "pptx-parser";

interface PptxSlide {
  slideNumber: number;
  texts?: Array<{
    text: string;
    x?: number;
    y?: number;
    fontSize?: number;
    fontFamily?: string;
    color?: string;
  }>;
  images?: Array<{
    src: string;
    x?: number;
    y?: number;
    width?: number;
    height?: number;
  }>;
  shapes?: Array<{
    type: string;
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    fill?: string;
  }>;
}

interface PptxImporterProps {
  onSlidesChange?: (slides: PptxSlide[]) => void;
  onError?: (error: string) => void;
  className?: string;
}

export default function PptxImporter({
  onSlidesChange,
  onError,
  className = "",
}: PptxImporterProps) {
  const [slides, setSlides] = useState<PptxSlide[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): boolean => {
    // Check file type
    const validTypes = [
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      ".pptx"
    ];
    
    const isValidType = validTypes.includes(file.type) || file.name.toLowerCase().endsWith('.pptx');
    
    if (!isValidType) {
      const errorMsg = "Chỉ hỗ trợ file .pptx";
      setError(errorMsg);
      onError?.(errorMsg);
      return false;
    }

    // Check file size (max 50MB)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      const errorMsg = "File quá lớn. Vui lòng chọn file nhỏ hơn 50MB";
      setError(errorMsg);
      onError?.(errorMsg);
      return false;
    }

    return true;
  };

  const processFile = async (file: File) => {
    if (!validateFile(file)) return;

    setIsProcessing(true);
    setError(null);
    setSelectedFile(file);

    try {
      const arrayBuffer = await file.arrayBuffer();
      console.log("Processing PPTX file:", file.name);
      
      const result = await parsePptx(arrayBuffer);
      console.log("PPTX parsing result:", result);

      // Transform the result to our expected format
      const transformedSlides: PptxSlide[] = result.slides?.map((slide: any, index: number) => ({
        slideNumber: index + 1,
        texts: slide.texts || [],
        images: slide.images || [],
        shapes: slide.shapes || [],
      })) || [];

      setSlides(transformedSlides);
      onSlidesChange?.(transformedSlides);
      
      toast.success(`Đã import thành công ${transformedSlides.length} slide từ file ${file.name}`);
    } catch (err) {
      console.error("Error parsing PPTX:", err);
      const errorMsg = "Không thể đọc file PPTX. Vui lòng kiểm tra lại file.";
      setError(errorMsg);
      onError?.(errorMsg);
      toast.error(errorMsg);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  const removeFile = () => {
    setSelectedFile(null);
    setSlides([]);
    setError(null);
    onSlidesChange?.([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
          dragActive
            ? "border-blue-500 bg-blue-50"
            : "border-gray-300 hover:border-gray-400"
        } ${isProcessing ? "pointer-events-none opacity-50" : ""}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => !isProcessing && fileInputRef.current?.click()}
      >
        <div className="flex flex-col justify-center items-center gap-2">
          {isProcessing ? (
            <Loader2 className="h-12 w-12 text-blue-500 animate-spin" />
          ) : (
            <Image
              src="/images/illustration/packing.svg"
              width="100"
              height="100"
              alt="Upload"
            />
          )}
          <p className="text-base font-medium font-questrial text-gray-900 mb-2">
            {isProcessing ? "Đang xử lý file PPTX..." : "Kéo thả file PPTX vào đây"}
          </p>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept=".pptx,application/vnd.openxmlformats-officedocument.presentationml.presentation"
          onChange={handleFileSelect}
          className="hidden"
          disabled={isProcessing}
        />
        <p className="text-sm text-gray-500">
          Chỉ hỗ trợ file .pptx (tối đa 50MB)
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
          <span className="text-red-700 text-sm">{error}</span>
        </div>
      )}

      {/* Selected File Info */}
      {selectedFile && !error && (
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-8 w-8 text-orange-500" />
              <div>
                <p className="font-medium font-questrial text-gray-900">
                  {selectedFile.name}
                </p>
                <p className="text-sm text-gray-500">
                  {formatFileSize(selectedFile.size)} • {slides.length} slide(s)
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={removeFile}
              className="text-gray-500 hover:text-red-500"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Slides Preview */}
      {slides.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-calsans text-gray-900">
            Nội dung đã import ({slides.length} slide):
          </h4>
          <div className="max-h-96 overflow-y-auto space-y-3">
            {slides.map((slide, index) => (
              <div key={index} className="border p-4 rounded-lg bg-white shadow-sm">
                <h5 className="font-medium font-calsans text-gray-800 mb-2">
                  Slide {slide.slideNumber}
                </h5>
                {slide.texts && slide.texts.length > 0 && (
                  <div className="space-y-1">
                    {slide.texts.map((text, i) => (
                      <p key={i} className="text-sm font-questrial text-gray-600">
                        • {text.text}
                      </p>
                    ))}
                  </div>
                )}
                {slide.images && slide.images.length > 0 && (
                  <p className="text-xs text-blue-600 mt-2">
                    📷 {slide.images.length} hình ảnh
                  </p>
                )}
                {slide.shapes && slide.shapes.length > 0 && (
                  <p className="text-xs text-green-600 mt-1">
                    🔷 {slide.shapes.length} hình dạng
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
