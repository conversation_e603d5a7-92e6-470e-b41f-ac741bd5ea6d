import React from "react";

interface ShapeData {
  type: string;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  fill?: string;
  border?: string;
  text?: string;
  svgPath?: string;
}

interface ShapeRendererProps {
  shape: ShapeData;
  maxWidth?: number;
  maxHeight?: number;
  showText?: boolean;
}

export default function ShapeRenderer({
  shape,
  maxWidth = 100,
  maxHeight = 80,
  showText = false,
}: ShapeRendererProps) {
  const size = Math.min(shape.width || 100, maxWidth);
  const height = Math.min(shape.height || 100, maxHeight);

  const svgProps = {
    width: size,
    height: height,
    viewBox: `0 0 ${size} ${height}`,
    style: { backgroundColor: "transparent" },
  };

  const fillColor = shape.fill || "#CCCCCC";
  const strokeColor =
    shape.border && shape.border !== "none"
      ? shape.border.split(" ")[2] || "#000000"
      : "none";
  const strokeWidth =
    shape.border && shape.border !== "none"
      ? parseInt(shape.border.split(" ")[0]) || 1
      : 0;

  const renderShape = () => {
    // Nếu có SVG path từ parser, dùng luôn
    if (shape.svgPath) {
      // Nếu là group, render trực tiếp SVG content
      if (shape.type === "group") {
        return (
          <g
            dangerouslySetInnerHTML={{
              __html: shape.svgPath.replace(/<g[^>]*>|<\/g>/g, ""),
            }}
          />
        );
      }

      // Cho shapes thường, apply fill và stroke
      return (
        <g
          fill={fillColor}
          stroke={strokeColor}
          strokeWidth={strokeWidth}
          dangerouslySetInnerHTML={{ __html: shape.svgPath }}
        />
      );
    }

    // Fallback to manual rendering
    switch (shape.type) {
      case "ellipse":
        return (
          <ellipse
            cx={size / 2}
            cy={height / 2}
            rx={size / 2 - strokeWidth}
            ry={height / 2 - strokeWidth}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      case "roundRect":
        return (
          <rect
            x={strokeWidth}
            y={strokeWidth}
            width={size - strokeWidth * 2}
            height={height - strokeWidth * 2}
            rx="8"
            ry="8"
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      case "triangle":
        return (
          <polygon
            points={`${size / 2},${strokeWidth} ${size - strokeWidth},${
              height - strokeWidth
            } ${strokeWidth},${height - strokeWidth}`}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      case "diamond":
        return (
          <polygon
            points={`${size / 2},${strokeWidth} ${size - strokeWidth},${
              height / 2
            } ${size / 2},${height - strokeWidth} ${strokeWidth},${height / 2}`}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      case "rightArrow":
        return (
          <polygon
            points={`${strokeWidth},${height * 0.3} ${size * 0.7},${
              height * 0.3
            } ${size * 0.7},${strokeWidth} ${size - strokeWidth},${
              height / 2
            } ${size * 0.7},${height - strokeWidth} ${size * 0.7},${
              height * 0.7
            } ${strokeWidth},${height * 0.7}`}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      case "leftArrow":
        return (
          <polygon
            points={`${strokeWidth},${height / 2} ${
              size * 0.3
            },${strokeWidth} ${size * 0.3},${height * 0.3} ${
              size - strokeWidth
            },${height * 0.3} ${size - strokeWidth},${height * 0.7} ${
              size * 0.3
            },${height * 0.7} ${size * 0.3},${height - strokeWidth}`}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      case "star5":
        const centerX = size / 2;
        const centerY = height / 2;
        const outerRadius = Math.min(size, height) / 2 - strokeWidth;
        const innerRadius = outerRadius * 0.4;
        let starPoints = "";

        for (let i = 0; i < 10; i++) {
          const angle = (i * Math.PI) / 5 - Math.PI / 2;
          const radius = i % 2 === 0 ? outerRadius : innerRadius;
          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);
          starPoints += `${x},${y} `;
        }

        return (
          <polygon
            points={starPoints.trim()}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      case "plus":
        return (
          <path
            d={`M ${size * 0.4} ${strokeWidth} L ${
              size * 0.6
            } ${strokeWidth} L ${size * 0.6} ${height * 0.4} L ${
              size - strokeWidth
            } ${height * 0.4} L ${size - strokeWidth} ${height * 0.6} L ${
              size * 0.6
            } ${height * 0.6} L ${size * 0.6} ${height - strokeWidth} L ${
              size * 0.4
            } ${height - strokeWidth} L ${size * 0.4} ${
              height * 0.6
            } L ${strokeWidth} ${height * 0.6} L ${strokeWidth} ${
              height * 0.4
            } L ${size * 0.4} ${height * 0.4} Z`}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      case "heart":
        return (
          <path
            d={`M ${size / 2} ${height * 0.8} C ${size / 2} ${
              height * 0.8
            } ${strokeWidth} ${height * 0.4} ${strokeWidth} ${
              height * 0.25
            } C ${strokeWidth} ${height * 0.1} ${size * 0.25} ${strokeWidth} ${
              size / 2
            } ${height * 0.3} C ${size * 0.75} ${strokeWidth} ${
              size - strokeWidth
            } ${height * 0.1} ${size - strokeWidth} ${height * 0.25} C ${
              size - strokeWidth
            } ${height * 0.4} ${size / 2} ${height * 0.8} ${size / 2} ${
              height * 0.8
            } Z`}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      case "hexagon":
        const hexPoints = [];
        for (let i = 0; i < 6; i++) {
          const angle = (i * Math.PI) / 3;
          const x = centerX + (outerRadius - strokeWidth) * Math.cos(angle);
          const y = centerY + (outerRadius - strokeWidth) * Math.sin(angle);
          hexPoints.push(`${x},${y}`);
        }

        return (
          <polygon
            points={hexPoints.join(" ")}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      case "flowChartProcess":
        return (
          <rect
            x={strokeWidth}
            y={strokeWidth}
            width={size - strokeWidth * 2}
            height={height - strokeWidth * 2}
            rx="4"
            ry="4"
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      case "flowChartDecision":
        return (
          <polygon
            points={`${size / 2},${strokeWidth} ${size - strokeWidth},${
              height / 2
            } ${size / 2},${height - strokeWidth} ${strokeWidth},${height / 2}`}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );

      default: // rect and others
        return (
          <rect
            x={strokeWidth}
            y={strokeWidth}
            width={size - strokeWidth * 2}
            height={height - strokeWidth * 2}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
          />
        );
    }
  };

  return (
    <div className="relative">
      <svg {...svgProps}>
        {renderShape()}
        {showText && shape.text && (
          <text
            x={size / 2}
            y={height / 2}
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize="10"
            fill="#333"
            fontFamily="Arial"
          >
            {shape.text.length > 10
              ? shape.text.substring(0, 10) + "..."
              : shape.text}
          </text>
        )}
      </svg>

      {/* Text below shape if not showing inside */}
      {!showText && shape.text && (
        <div className="text-xs text-center text-gray-600 mt-1 truncate">
          {shape.text}
        </div>
      )}
    </div>
  );
}
