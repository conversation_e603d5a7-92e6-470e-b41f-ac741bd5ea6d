import PptxGenJS from "pptxgenjs";
import { Slide } from "@/types/slideEditor";

export async function exportToPptx(slides: Slide[], filename: string = "slide-design.pptx") {
  try {
    const pptx = new PptxGenJS();
    
    // Set presentation properties
    pptx.author = "Slide Editor";
    pptx.company = "Planbook";
    pptx.title = "Generated Presentation";

    for (const slide of slides) {
      const pptxSlide = pptx.addSlide();
      
      // Set slide background
      pptxSlide.background = { color: "FFFFFF" };

      for (const element of slide.elements) {
        switch (element.type) {
          case "text":
            // Convert pixels to inches (96 DPI)
            const textOptions = {
              x: element.x / 96,
              y: element.y / 96,
              w: element.width / 96,
              h: element.height / 96,
              fontSize: element.style.fontSize || 16,
              color: element.style.color?.replace("#", "") || "000000",
              fontFace: element.style.fontFamily || "Arial",
              bold: element.style.bold || false,
              italic: element.style.italic || false,
              align: element.style.textAlign || "left",
              valign: "top",
            };

            // Add background if specified
            if (element.style.backgroundColor && element.style.backgroundColor !== "transparent") {
              textOptions.fill = {
                color: element.style.backgroundColor.replace("#", "")
              };
            }

            pptxSlide.addText(element.text, textOptions);
            break;

          case "image":
            try {
              // For base64 images
              if (element.src.startsWith("data:")) {
                pptxSlide.addImage({
                  data: element.src,
                  x: element.x / 96,
                  y: element.y / 96,
                  w: element.width / 96,
                  h: element.height / 96,
                });
              } else {
                // For URL images - note: may not work in all cases due to CORS
                pptxSlide.addImage({
                  path: element.src,
                  x: element.x / 96,
                  y: element.y / 96,
                  w: element.width / 96,
                  h: element.height / 96,
                });
              }
            } catch (error) {
              console.warn("Failed to add image:", error);
              // Add placeholder text instead
              pptxSlide.addText("Image could not be exported", {
                x: element.x / 96,
                y: element.y / 96,
                w: element.width / 96,
                h: element.height / 96,
                fontSize: 12,
                color: "666666",
                align: "center",
                valign: "middle",
              });
            }
            break;

          case "shape":
            const shapeOptions = {
              x: element.x / 96,
              y: element.y / 96,
              w: element.width / 96,
              h: element.height / 96,
              fill: { color: element.fill?.replace("#", "") || "3B82F6" },
              line: {
                color: element.stroke?.replace("#", "") || "1E40AF",
                width: (element.strokeWidth || 2) / 96,
              },
            };

            switch (element.shapeType) {
              case "rectangle":
                pptxSlide.addShape(pptx.ShapeType.rect, shapeOptions);
                break;
              case "circle":
                pptxSlide.addShape(pptx.ShapeType.ellipse, shapeOptions);
                break;
              case "triangle":
                pptxSlide.addShape(pptx.ShapeType.triangle, shapeOptions);
                break;
              default:
                pptxSlide.addShape(pptx.ShapeType.rect, shapeOptions);
            }
            break;

          case "table":
            const tableData = element.rows.map(row => 
              row.map(cell => ({
                text: cell,
                options: {
                  fontSize: element.style?.fontSize || 14,
                  color: "000000",
                  fill: element.style?.backgroundColor?.replace("#", "") || "FFFFFF",
                  border: [
                    { pt: 1, color: element.style?.borderColor?.replace("#", "") || "000000" },
                    { pt: 1, color: element.style?.borderColor?.replace("#", "") || "000000" },
                    { pt: 1, color: element.style?.borderColor?.replace("#", "") || "000000" },
                    { pt: 1, color: element.style?.borderColor?.replace("#", "") || "000000" },
                  ],
                }
              }))
            );

            pptxSlide.addTable(tableData, {
              x: element.x / 96,
              y: element.y / 96,
              w: element.width / 96,
              h: element.height / 96,
            });
            break;
        }
      }
    }

    // Generate and download the PPTX file
    await pptx.writeFile({ fileName: filename });
    return true;
  } catch (error) {
    console.error("Error exporting to PPTX:", error);
    throw error;
  }
}

// Helper function to convert hex color to RGB
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

// Alternative export function with better error handling
export async function exportToPptxSafe(slides: Slide[], filename: string = "slide-design.pptx") {
  try {
    await exportToPptx(slides, filename);
    return { success: true, message: "PPTX exported successfully!" };
  } catch (error) {
    console.error("PPTX export failed:", error);
    return { 
      success: false, 
      message: `Export failed: ${error instanceof Error ? error.message : "Unknown error"}` 
    };
  }
}
