import { PptxSlide, PptxParseResult } from "@/lib/pptx/types";
import { SlideElement, Slide } from "@/types/slideEditor";

// Convert PPTX parse result to Slide Editor format
export function convertPptxToEditor(pptxResult: PptxParseResult): {
  slides: Slide[];
  canvasSize: { width: number; height: number };
} {
  const slides: Slide[] = pptxResult.slides.map((pptxSlide, index) => {
    const elements: SlideElement[] = [];

    // Convert texts
    pptxSlide.texts.forEach((text, textIndex) => {
      elements.push({
        type: "text",
        id: `text-${pptxSlide.slideNumber}-${textIndex}`,
        x: text.x || 0,
        y: text.y || 0,
        width: 200, // Default width, can be adjusted
        height: text.estimatedHeight || 50,
        text: text.text,
        style: {
          fontSize: text.fontSize || 16,
          color: text.color || "#000000",
          backgroundColor: "transparent",
          fontFamily: text.fontFamily || "Arial",
          bold: text.bold || false,
          italic: text.italic || false,
          textAlign: (text.textAlign as "left" | "center" | "right") || "left",
        },
      });
    });

    // Convert images
    pptxSlide.images.forEach((image, imageIndex) => {
      elements.push({
        type: "image",
        id: `image-${pptxSlide.slideNumber}-${imageIndex}`,
        x: image.x || 0,
        y: image.y || 0,
        width: image.width || 200,
        height: image.height || 150,
        src: image.src,
        alt: image.name || "Imported image",
      });
    });

    // Convert shapes
    pptxSlide.shapes.forEach((shape, shapeIndex) => {
      if (shape.type === "table" && shape.tableData) {
        // Convert table
        elements.push({
          type: "table",
          id: `table-${pptxSlide.slideNumber}-${shapeIndex}`,
          x: shape.x || 0,
          y: shape.y || 0,
          width: shape.width || 300,
          height: shape.height || 200,
          rows: shape.tableData.map(row => 
            row.cells.map(cell => cell.text || "")
          ),
          style: {
            borderColor: "#000000",
            backgroundColor: "#ffffff",
            fontSize: 14,
          },
        });
      } else if (shape.type !== "group") {
        // Convert regular shape
        let shapeType = "rectangle";
        if (shape.type === "ellipse") shapeType = "circle";
        else if (shape.type === "triangle") shapeType = "triangle";

        elements.push({
          type: "shape",
          id: `shape-${pptxSlide.slideNumber}-${shapeIndex}`,
          x: shape.x || 0,
          y: shape.y || 0,
          width: shape.width || 100,
          height: shape.height || 100,
          shapeType: shapeType,
          svgPath: shape.svgPath || "",
          fill: shape.fill || "#3B82F6",
          stroke: shape.border || "#1E40AF",
          strokeWidth: 2,
        });
      }
    });

    return {
      id: `slide-${pptxSlide.slideNumber}`,
      name: `Slide ${pptxSlide.slideNumber}`,
      elements: elements,
    };
  });

  return {
    slides: slides.length > 0 ? slides : [{
      id: "slide-1",
      name: "Slide 1", 
      elements: []
    }],
    canvasSize: { width: 800, height: 600 },
  };
}

// Convert Editor format back to PPTX-like structure (for export)
export function convertEditorToPptx(slides: Slide[]): PptxParseResult {
  const pptxSlides: PptxSlide[] = slides.map((slide, index) => {
    const pptxSlide: PptxSlide = {
      slideNumber: index + 1,
      texts: [],
      images: [],
      shapes: [],
      elements: [],
    };

    slide.elements.forEach(element => {
      switch (element.type) {
        case "text":
          pptxSlide.texts.push({
            text: element.text,
            x: element.x,
            y: element.y,
            fontSize: element.style.fontSize,
            fontFamily: element.style.fontFamily,
            color: element.style.color,
            bold: element.style.bold,
            italic: element.style.italic,
            textAlign: element.style.textAlign,
          });
          break;

        case "image":
          pptxSlide.images.push({
            src: element.src,
            x: element.x,
            y: element.y,
            width: element.width,
            height: element.height,
            name: element.alt || "image",
          });
          break;

        case "shape":
          pptxSlide.shapes.push({
            type: element.shapeType === "circle" ? "ellipse" : element.shapeType,
            x: element.x,
            y: element.y,
            width: element.width,
            height: element.height,
            fill: element.fill,
            border: element.stroke,
            svgPath: element.svgPath,
          });
          break;

        case "table":
          pptxSlide.shapes.push({
            type: "table",
            x: element.x,
            y: element.y,
            width: element.width,
            height: element.height,
            tableData: element.rows.map(row => ({
              cells: row.map(cellText => ({
                text: cellText,
                style: {
                  fontSize: element.style?.fontSize,
                  backgroundColor: element.style?.backgroundColor,
                  color: "#000000",
                },
              })),
            })),
          });
          break;
      }
    });

    return pptxSlide;
  });

  return {
    slides: pptxSlides,
    images: [], // Will be populated from slide images
  };
}
