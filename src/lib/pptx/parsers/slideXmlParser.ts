import { parseStringPromise } from "xml2js";
import { PptxSlide, ExtractedImage } from "../types";
import { processShapeTreeElements } from "../processors/slideProcessor";
import { convertToUnifiedElements } from "./unifyElements";

// Parse individual slide XML with image mapping
export const parseSlideXml = async (
  slideXml: string,
  slideNumber: number,
  extractedImages: ExtractedImage[] = []
): Promise<PptxSlide> => {
  const slide: PptxSlide = {
    slideNumber,
    elements: [], // New unified elements array
    // Backward compatibility
    texts: [],
    images: [],
    shapes: [],
  };

  try {
    const result = await parseStringPromise(slideXml);
    const slideData = result["p:sld"];

    if (!slideData) {
      console.warn(`❌ No slide data found for slide ${slideNumber}`);
      return slide;
    }

    const commonSlideData = slideData["p:cSld"]?.[0];
    if (!commonSlideData) {
      console.warn(`❌ No common slide data found for slide ${slideNumber}`);
      return slide;
    }

    const shapeTree = commonSlideData["p:spTree"]?.[0];
    if (!shapeTree) {
      console.warn(`❌ No shape tree found for slide ${slideNumber}`);
      return slide;
    }

    console.log(
      `🔍 Processing slide ${slideNumber}, shapeTree keys:`,
      Object.keys(shapeTree)
    );

    // Process all types of elements
    processShapeTreeElements(shapeTree, slide, extractedImages);

    // Convert legacy arrays to unified elements array
    convertToUnifiedElements(slide);

    console.log(
      `Slide ${slideNumber} processed: ${slide.elements.length} elements (${slide.texts.length} texts, ${slide.images.length} images, ${slide.shapes.length} shapes)`
    );

    // Debug: log elements with tables
    const tableElements = slide.elements.filter((el) => el.type === "table");
    if (tableElements.length > 0) {
      console.log(
        `Found ${tableElements.length} table elements:`,
        tableElements
      );
    }

    return slide;
  } catch (error) {
    console.error(`❌ Error parsing slide ${slideNumber}:`, error);
    return {
      slideNumber,
      elements: [],
      texts: [],
      images: [],
      shapes: [],
    };
  }
};
