import { PptxSlide, ExtractedImage } from "../types";
import { extractTransform } from "../utils/transform";
import { parseShapeProperties } from "./shapeParser";

// Process group shape with proper transform and recursive handling
export const processGroupShape = (
  group: any,
  slide: PptxSlide,
  extractedImages: ExtractedImage[],
  parentTransform: { x: number; y: number } = { x: 0, y: 0 }
) => {
  const childrenSvg: string[] = [];

  const shapeTree = group["p:spTree"] || group;
  const groupTransform = extractTransform(group["p:grpSpPr"]?.[0]) || {
    x: 0,
    y: 0,
  };

  const combinedTransform = {
    x: parentTransform.x + groupTransform.x,
    y: parentTransform.y + groupTransform.y,
  };

  console.log(
    `📦 Processing group: parent(${parentTransform.x},${parentTransform.y}) + group(${groupTransform.x},${groupTransform.y}) = combined(${combinedTransform.x},${combinedTransform.y})`
  );

  // Parse shapes inside group
  const shapes = shapeTree["p:sp"] || [];
  shapes.forEach((shape: any) => {
    const spPr = shape["p:spPr"]?.[0];
    if (spPr) {
      const shapeData = parseShapeProperties(spPr);
      if (shapeData?.svgPath) {
        const transform = `translate(${shapeData.x}, ${shapeData.y})`;
        childrenSvg.push(
          `<g transform="${transform}">${shapeData.svgPath}</g>`
        );
        console.log(
          `  ➕ Added child shape: ${shapeData.type} at (${shapeData.x}, ${shapeData.y})`
        );
      }
    }
  });

  // Parse nested groups (recursive)
  const nestedGroups = shapeTree["p:grpSp"] || [];
  nestedGroups.forEach((nested: any) => {
    console.log("Processing nested group");
    const nestedSlide: PptxSlide = {
      slideNumber: slide.slideNumber,
      texts: [],
      images: [],
      shapes: [],
    };
    processGroupShape(nested, nestedSlide, extractedImages, { x: 0, y: 0 });
    nestedSlide.shapes.forEach((s) => {
      if (s.svgPath) {
        const transform = `translate(${s.x}, ${s.y})`;
        childrenSvg.push(`<g transform="${transform}">${s.svgPath}</g>`);
      }
    });
  });

  // Parse pictures in group
  const pictures = shapeTree["p:pic"] || [];
  pictures.forEach((picture: any, index: number) => {
    const pictureTransform = extractTransform(picture["p:spPr"]?.[0]) || {
      x: 0,
      y: 0,
    };
    const actualImage = extractedImages[index] || null;

    if (actualImage) {
      const transform = `translate(${pictureTransform.x}, ${pictureTransform.y})`;
      const imageSvg = `<image href="${actualImage.src}" width="${
        pictureTransform.width || 200
      }" height="${pictureTransform.height || 150}" />`;
      childrenSvg.push(`<g transform="${transform}">${imageSvg}</g>`);
      console.log(
        `Added group image at (${pictureTransform.x}, ${pictureTransform.y})`
      );
    }
  });

  // Combine children into group SVG
  if (childrenSvg.length > 0) {
    const svgGroup = `<g transform="translate(${combinedTransform.x}, ${
      combinedTransform.y
    })">
${childrenSvg.join("\n")}
</g>`;

    // Push to slide as one shape
    slide.shapes.push({
      type: "group",
      x: combinedTransform.x,
      y: combinedTransform.y,
      width: 0,
      height: 0,
      fill: "transparent",
      border: "none",
      svgPath: svgGroup,
    });

    console.log(
      `✅ Parsed group shape with ${childrenSvg.length} children at (${combinedTransform.x}, ${combinedTransform.y})`
    );
  }
};
