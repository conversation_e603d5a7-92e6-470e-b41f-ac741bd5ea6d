import { PptxSlide, SlideElement } from "../types";

// Convert legacy arrays to unified elements array
export const convertToUnifiedElements = (slide: PptxSlide) => {
  const elements: SlideElement[] = [];

  // Convert texts
  slide.texts.forEach((text) => {
    elements.push({
      type: "text",
      x: text.x || 0,
      y: text.y || 0,
      text: text.text,
      style: {
        fontSize: text.fontSize,
        fontFamily: text.fontFamily,
        color: text.color,
      },
    });
  });

  // Convert images
  slide.images.forEach((image) => {
    elements.push({
      type: "image",
      x: image.x || 0,
      y: image.y || 0,
      width: image.width || 200,
      height: image.height || 150,
      src: image.src,
      name: image.name,
    });
  });

  // Convert shapes
  slide.shapes.forEach((shape) => {
    if (shape.type === "group") {
      elements.push({
        type: "group",
        x: shape.x || 0,
        y: shape.y || 0,
        width: shape.width || 0,
        height: shape.height || 0,
        children: [], // Groups will be processed separately
        svgPath: shape.svgPath || "",
      });
    } else if (shape.type === "table" && shape.tableData) {
      // Convert table with structured data
      elements.push({
        type: "table",
        x: shape.x || 0,
        y: shape.y || 0,
        width: shape.width || 400,
        height: shape.height || 200,
        rows: shape.tableData,
        svgPath: shape.svgPath,
      });
    } else {
      elements.push({
        type: "shape",
        x: shape.x || 0,
        y: shape.y || 0,
        width: shape.width || 100,
        height: shape.height || 100,
        svgPath: shape.svgPath || "",
        fill: shape.fill,
        border: shape.border,
        shapeType: shape.type,
        shapeId: shape.shapeId,
      });
    }
  });

  slide.elements = elements;
  
  console.log(`Unified ${elements.length} elements: ${elements.map(e => e.type).join(', ')}`);
};
