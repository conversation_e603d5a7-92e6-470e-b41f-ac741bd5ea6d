export interface ExtractedImage {
  src: string; // base64 data URL
  name: string; // original filename
  path: string; // path in PPTX
  type: string; // image type (png, jpg, etc.)
}

// Unified slide element types
export type SlideElement =
  | {
      type: "text";
      x: number;
      y: number;
      text: string;
      style?: {
        fontSize?: number;
        fontFamily?: string;
        color?: string;
      };
    }
  | {
      type: "image";
      x: number;
      y: number;
      width: number;
      height: number;
      src: string;
      name?: string;
    }
  | {
      type: "shape";
      x: number;
      y: number;
      width: number;
      height: number;
      svgPath: string;
      fill?: string;
      border?: string;
      shapeType?: string;
    }
  | {
      type: "group";
      x: number;
      y: number;
      width: number;
      height: number;
      children: SlideElement[];
      svgPath: string;
    }
  | {
      type: "table";
      x: number;
      y: number;
      width: number;
      height: number;
      rows: string[][];
    };

export interface PptxSlide {
  slideNumber: number;
  elements: SlideElement[];
  // Backward compatibility - deprecated
  texts: Array<{
    text: string;
    x?: number;
    y?: number;
    fontSize?: number;
    fontFamily?: string;
    color?: string;
  }>;
  images: Array<{
    src: string;
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    name?: string;
  }>;
  shapes: Array<{
    type: string;
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    fill?: string;
    border?: string;
    text?: string;
    svgPath?: string;
  }>;
}

export interface PptxParseResult {
  slides: PptxSlide[];
  images: ExtractedImage[]; // All extracted images
  metadata: {
    totalSlides: number;
    title?: string;
    author?: string;
    createdDate?: string;
  };
}

export interface Transform {
  x: number;
  y: number;
  width?: number;
  height?: number;
}

// Utility functions
export const getPptxStats = (slides: PptxSlide[]) => ({
  totalSlides: slides.length,
  totalElements: slides.reduce((s, slide) => s + slide.elements.length, 0),
  totalTexts: slides.reduce((s, slide) => s + slide.texts.length, 0),
  totalImages: slides.reduce((s, slide) => s + slide.images.length, 0),
  totalShapes: slides.reduce((s, slide) => s + slide.shapes.length, 0),
  elementsByType: slides.reduce((acc, slide) => {
    slide.elements.forEach((element) => {
      acc[element.type] = (acc[element.type] || 0) + 1;
    });
    return acc;
  }, {} as Record<string, number>),
});

export interface ParseError {
  slide: number;
  error: any;
  message: string;
}
