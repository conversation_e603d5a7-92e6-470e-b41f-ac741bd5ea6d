// Supported shape types
const SUPPORTED_SHAPE_TYPES = [
  "rect",
  "roundRect",
  "ellipse",
  "triangle",
  "diamond",
  "rightArrow",
  "leftArrow",
  "star5",
  "plus",
  "heart",
  "hexagon",
  "flowChartProcess",
  "flowChartDecision",
  "group",
  // Additional common shapes
  "pentagon",
  "octagon",
  "parallelogram",
  "trapezoid",
  "cloud",
  "sun",
  "moon",
  "lightningBolt",
];

// Generate SVG path for shape type
export const generateSvgPath = (
  shapeType: string,
  width: number,
  height: number,
  childrenSvg?: string
): string => {
  const w = width;
  const h = height;

  // Log unsupported shape types for debugging
  if (!SUPPORTED_SHAPE_TYPES.includes(shapeType)) {
    console.warn(`Unsupported shapeType: ${shapeType}, fallback to rect`);
  }

  switch (shapeType) {
    case "ellipse":
      return `<ellipse cx="${w / 2}" cy="${h / 2}" rx="${w / 2}" ry="${
        h / 2
      }" />`;

    case "roundRect":
      const rx = Math.min(w, h) * 0.1;
      return `<rect x="0" y="0" width="${w}" height="${h}" rx="${rx}" ry="${rx}" />`;

    case "triangle":
      return `<polygon points="${w / 2},0 ${w},${h} 0,${h}" />`;

    case "diamond":
      return `<polygon points="${w / 2},0 ${w},${h / 2} ${w / 2},${h} 0,${
        h / 2
      }" />`;

    case "rightArrow":
      return `<polygon points="0,${h * 0.3} ${w * 0.7},${h * 0.3} ${
        w * 0.7
      },0 ${w},${h / 2} ${w * 0.7},${h} ${w * 0.7},${h * 0.7} 0,${h * 0.7}" />`;

    case "leftArrow":
      return `<polygon points="0,${h / 2} ${w * 0.3},0 ${w * 0.3},${
        h * 0.3
      } ${w},${h * 0.3} ${w},${h * 0.7} ${w * 0.3},${h * 0.7} ${
        w * 0.3
      },${h}" />`;

    case "star5":
      const centerX = w / 2;
      const centerY = h / 2;
      const outerRadius = Math.min(w, h) / 2;
      const innerRadius = outerRadius * 0.4;
      let points = "";

      for (let i = 0; i < 10; i++) {
        const angle = (i * Math.PI) / 5 - Math.PI / 2;
        const radius = i % 2 === 0 ? outerRadius : innerRadius;
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);
        points += `${x},${y} `;
      }

      return `<polygon points="${points.trim()}" />`;

    case "plus":
      return `<path d="M ${w * 0.4} 0 L ${w * 0.6} 0 L ${w * 0.6} ${
        h * 0.4
      } L ${w} ${h * 0.4} L ${w} ${h * 0.6} L ${w * 0.6} ${h * 0.6} L ${
        w * 0.6
      } ${h} L ${w * 0.4} ${h} L ${w * 0.4} ${h * 0.6} L 0 ${h * 0.6} L 0 ${
        h * 0.4
      } L ${w * 0.4} ${h * 0.4} Z" />`;

    case "heart":
      return `<path d="M ${w / 2} ${h * 0.8} C ${w / 2} ${h * 0.8} 0 ${
        h * 0.4
      } 0 ${h * 0.25} C 0 ${h * 0.1} ${w * 0.25} 0 ${w / 2} ${h * 0.3} C ${
        w * 0.75
      } 0 ${w} ${h * 0.1} ${w} ${h * 0.25} C ${w} ${h * 0.4} ${w / 2} ${
        h * 0.8
      } ${w / 2} ${h * 0.8} Z" />`;

    case "hexagon":
      const hexCenterX = w / 2;
      const hexCenterY = h / 2;
      const hexPoints = [];
      for (let i = 0; i < 6; i++) {
        const angle = (i * Math.PI) / 3;
        const x = hexCenterX + (w / 2) * Math.cos(angle);
        const y = hexCenterY + (h / 2) * Math.sin(angle);
        hexPoints.push(`${x},${y}`);
      }
      return `<polygon points="${hexPoints.join(" ")}" />`;

    case "flowChartProcess":
      return `<rect x="0" y="0" width="${w}" height="${h}" rx="4" ry="4" />`;

    case "flowChartDecision":
      return `<polygon points="${w / 2},0 ${w},${h / 2} ${w / 2},${h} 0,${
        h / 2
      }" />`;

    case "pentagon":
      const pentagonPoints = [];
      for (let i = 0; i < 5; i++) {
        const angle = (i * 2 * Math.PI) / 5 - Math.PI / 2;
        const x = w / 2 + (w / 2) * Math.cos(angle);
        const y = h / 2 + (h / 2) * Math.sin(angle);
        pentagonPoints.push(`${x},${y}`);
      }
      return `<polygon points="${pentagonPoints.join(" ")}" />`;

    case "octagon":
      const octagonPoints = [];
      for (let i = 0; i < 8; i++) {
        const angle = (i * 2 * Math.PI) / 8 - Math.PI / 2;
        const x = w / 2 + (w / 2) * Math.cos(angle);
        const y = h / 2 + (h / 2) * Math.sin(angle);
        octagonPoints.push(`${x},${y}`);
      }
      return `<polygon points="${octagonPoints.join(" ")}" />`;

    case "parallelogram":
      const offset = w * 0.2;
      return `<polygon points="${offset},0 ${w},0 ${
        w - offset
      },${h} 0,${h}" />`;

    case "trapezoid":
      const topOffset = w * 0.2;
      return `<polygon points="${topOffset},0 ${
        w - topOffset
      },0 ${w},${h} 0,${h}" />`;

    case "cloud":
      return `<path d="M ${w * 0.2} ${h * 0.6}
               C ${w * 0.1} ${h * 0.4} ${w * 0.1} ${h * 0.2} ${w * 0.3} ${
        h * 0.2
      }
               C ${w * 0.3} ${h * 0.1} ${w * 0.5} ${h * 0.1} ${w * 0.6} ${
        h * 0.2
      }
               C ${w * 0.8} ${h * 0.1} ${w * 0.9} ${h * 0.3} ${w * 0.8} ${
        h * 0.4
      }
               C ${w * 0.9} ${h * 0.6} ${w * 0.8} ${h * 0.8} ${w * 0.6} ${
        h * 0.7
      }
               C ${w * 0.4} ${h * 0.8} ${w * 0.2} ${h * 0.8} ${w * 0.2} ${
        h * 0.6
      } Z" />`;

    case "sun":
      let sunPath = `<circle cx="${w / 2}" cy="${h / 2}" r="${
        Math.min(w, h) * 0.3
      }" />`;
      for (let i = 0; i < 8; i++) {
        const angle = (i * Math.PI) / 4;
        const x1 = w / 2 + Math.cos(angle) * (Math.min(w, h) * 0.35);
        const y1 = h / 2 + Math.sin(angle) * (Math.min(w, h) * 0.35);
        const x2 = w / 2 + Math.cos(angle) * (Math.min(w, h) * 0.45);
        const y2 = h / 2 + Math.sin(angle) * (Math.min(w, h) * 0.45);
        sunPath += `<line x1="${x1}" y1="${y1}" x2="${x2}" y2="${y2}" stroke="currentColor" stroke-width="2" />`;
      }
      return sunPath;

    case "moon":
      return `<path d="M ${w * 0.3} ${h * 0.2}
               C ${w * 0.7} ${h * 0.2} ${w * 0.8} ${h * 0.8} ${w * 0.3} ${
        h * 0.8
      }
               C ${w * 0.5} ${h * 0.6} ${w * 0.5} ${h * 0.4} ${w * 0.3} ${
        h * 0.2
      } Z" />`;

    case "lightningBolt":
      return `<polygon points="${w * 0.4},0 ${w * 0.7},${h * 0.4} ${w * 0.6},${
        h * 0.4
      } ${w},${h} ${w * 0.3},${h * 0.6} ${w * 0.4},${h * 0.6}" />`;

    case "group":
      return childrenSvg || `<g></g>`;

    default: // Fallback with debug info
      return `<rect x="0" y="0" width="${w}" height="${h}" fill="#EEE" stroke="#999" stroke-width="1" />
              <text x="${w / 2}" y="${
        h / 2
      }" text-anchor="middle" dominant-baseline="middle" font-size="${
        Math.min(w, h) * 0.1
      }" fill="#666">
                ${shapeType}
              </text>`;
  }
};
