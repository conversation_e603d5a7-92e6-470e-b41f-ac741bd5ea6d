// Generate SVG path for shape type
export const generateSvgPath = (
  shapeType: string,
  width: number,
  height: number,
  childrenSvg?: string
): string => {
  const w = width;
  const h = height;

  switch (shapeType) {
    case "ellipse":
      return `<ellipse cx="${w / 2}" cy="${h / 2}" rx="${w / 2}" ry="${
        h / 2
      }" />`;

    case "roundRect":
      const rx = Math.min(w, h) * 0.1;
      return `<rect x="0" y="0" width="${w}" height="${h}" rx="${rx}" ry="${rx}" />`;

    case "triangle":
      return `<polygon points="${w / 2},0 ${w},${h} 0,${h}" />`;

    case "diamond":
      return `<polygon points="${w / 2},0 ${w},${h / 2} ${w / 2},${h} 0,${
        h / 2
      }" />`;

    case "rightArrow":
      return `<polygon points="0,${h * 0.3} ${w * 0.7},${h * 0.3} ${
        w * 0.7
      },0 ${w},${h / 2} ${w * 0.7},${h} ${w * 0.7},${h * 0.7} 0,${
        h * 0.7
      }" />`;

    case "leftArrow":
      return `<polygon points="0,${h / 2} ${w * 0.3},0 ${w * 0.3},${
        h * 0.3
      } ${w},${h * 0.3} ${w},${h * 0.7} ${w * 0.3},${h * 0.7} ${w * 0.3},${h}" />`;

    case "star5":
      const centerX = w / 2;
      const centerY = h / 2;
      const outerRadius = Math.min(w, h) / 2;
      const innerRadius = outerRadius * 0.4;
      let points = "";

      for (let i = 0; i < 10; i++) {
        const angle = (i * Math.PI) / 5 - Math.PI / 2;
        const radius = i % 2 === 0 ? outerRadius : innerRadius;
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);
        points += `${x},${y} `;
      }

      return `<polygon points="${points.trim()}" />`;

    case "plus":
      return `<path d="M ${w * 0.4} 0 L ${w * 0.6} 0 L ${w * 0.6} ${
        h * 0.4
      } L ${w} ${h * 0.4} L ${w} ${h * 0.6} L ${w * 0.6} ${h * 0.6} L ${
        w * 0.6
      } ${h} L ${w * 0.4} ${h} L ${w * 0.4} ${h * 0.6} L 0 ${
        h * 0.6
      } L 0 ${h * 0.4} L ${w * 0.4} ${h * 0.4} Z" />`;

    case "heart":
      return `<path d="M ${w / 2} ${h * 0.8} C ${w / 2} ${h * 0.8} 0 ${
        h * 0.4
      } 0 ${h * 0.25} C 0 ${h * 0.1} ${w * 0.25} 0 ${w / 2} ${
        h * 0.3
      } C ${w * 0.75} 0 ${w} ${h * 0.1} ${w} ${h * 0.25} C ${w} ${
        h * 0.4
      } ${w / 2} ${h * 0.8} ${w / 2} ${h * 0.8} Z" />`;

    case "hexagon":
      const hexCenterX = w / 2;
      const hexCenterY = h / 2;
      const hexPoints = [];
      for (let i = 0; i < 6; i++) {
        const angle = (i * Math.PI) / 3;
        const x = hexCenterX + (w / 2) * Math.cos(angle);
        const y = hexCenterY + (h / 2) * Math.sin(angle);
        hexPoints.push(`${x},${y}`);
      }
      return `<polygon points="${hexPoints.join(" ")}" />`;

    case "flowChartProcess":
      return `<rect x="0" y="0" width="${w}" height="${h}" rx="4" ry="4" />`;

    case "flowChartDecision":
      return `<polygon points="${w / 2},0 ${w},${h / 2} ${w / 2},${h} 0,${
        h / 2
      }" />`;

    case "group":
      return childrenSvg || `<g></g>`;

    default: // rect
      return `<rect x="0" y="0" width="${w}" height="${h}" />`;
  }
};
