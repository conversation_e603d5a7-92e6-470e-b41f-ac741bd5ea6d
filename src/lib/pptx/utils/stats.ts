import { PptxSlide, SlideElementType } from "../types";

// Utility functions for PPTX statistics
export const getPptxStats = (slides: PptxSlide[]) => ({
  totalSlides: slides.length,
  totalElements: slides.reduce((s, slide) => s + slide.elements.length, 0),
  totalTexts: slides.reduce((s, slide) => s + slide.texts.length, 0),
  totalImages: slides.reduce((s, slide) => s + slide.images.length, 0),
  totalShapes: slides.reduce((s, slide) => s + slide.shapes.length, 0),
  elementsByType: slides.reduce((acc, slide) => {
    slide.elements.forEach((element) => {
      acc[element.type] = (acc[element.type] || 0) + 1;
    });
    return acc;
  }, {} as Record<SlideElementType, number>),
});

// Get elements by type across all slides
export const getElementsByType = (slides: PptxSlide[], type: SlideElementType) => {
  return slides.flatMap(slide => 
    slide.elements.filter(element => element.type === type)
  );
};

// Get slide statistics
export const getSlideStats = (slide: PptxSlide) => ({
  slideNumber: slide.slideNumber,
  totalElements: slide.elements.length,
  elementsByType: slide.elements.reduce((acc, element) => {
    acc[element.type] = (acc[element.type] || 0) + 1;
    return acc;
  }, {} as Record<SlideElementType, number>),
});
