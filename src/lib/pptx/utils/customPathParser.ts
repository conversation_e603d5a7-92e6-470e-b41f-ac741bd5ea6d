// Parse custom geometry from PowerPoint XML to SVG path
export function parseCustomPathToSvg(
  custGeomXml: any,
  targetWidth: number,
  targetHeight: number
): string {
  try {
    const pathNode = custGeomXml?.["a:pathLst"]?.[0]?.["a:path"]?.[0];
    if (!pathNode) {
      console.warn("No path found in custom geometry");
      return "";
    }

    // Get path dimensions (default PowerPoint coordinate system)
    const w = parseInt(pathNode.$?.w) || 21600;
    const h = parseInt(pathNode.$?.h) || 21600;
    
    // Calculate scale factors
    const scaleX = targetWidth / w;
    const scaleY = targetHeight / h;

    console.log(`Custom path: ${w}x${h} → ${targetWidth}x${targetHeight} (scale: ${scaleX.toFixed(3)}, ${scaleY.toFixed(3)})`);

    const commands: string[] = [];

    // Helper function to convert point coordinates
    const pointStr = (pt: any) => {
      const x = parseInt(pt.$.x || "0") * scaleX;
      const y = parseInt(pt.$.y || "0") * scaleY;
      return `${x.toFixed(2)},${y.toFixed(2)}`;
    };

    // Process all path commands
    for (const key in pathNode) {
      if (!pathNode[key] || key === '$') continue;
      
      const items = Array.isArray(pathNode[key]) ? pathNode[key] : [pathNode[key]];

      items.forEach((item: any) => {
        switch (key) {
          case "a:moveTo": {
            const pt = item["a:pt"]?.[0];
            if (pt) {
              commands.push(`M ${pointStr(pt)}`);
            }
            break;
          }
          
          case "a:lnTo": {
            const pt = item["a:pt"]?.[0];
            if (pt) {
              commands.push(`L ${pointStr(pt)}`);
            }
            break;
          }
          
          case "a:arcTo": {
            const arc = item.$ || {};
            const pt = item["a:pt"]?.[0];
            if (pt) {
              const wR = parseInt(arc.wR || "0") * scaleX;
              const hR = parseInt(arc.hR || "0") * scaleY;
              const stAng = parseInt(arc.stAng || "0") / 60000; // Convert from 60000ths of degree
              const swAng = parseInt(arc.swAng || "0") / 60000;
              
              // Simplified arc - for complex arcs, you'd need trigonometry
              const largeArcFlag = Math.abs(swAng) > 180 ? 1 : 0;
              const sweepFlag = swAng > 0 ? 1 : 0;
              
              commands.push(`A ${wR.toFixed(2)} ${hR.toFixed(2)} 0 ${largeArcFlag} ${sweepFlag} ${pointStr(pt)}`);
            }
            break;
          }
          
          case "a:quadBezTo": {
            const pts = item["a:pt"] || [];
            if (pts.length === 2) {
              const [control, end] = pts;
              commands.push(`Q ${pointStr(control)} ${pointStr(end)}`);
            }
            break;
          }
          
          case "a:cubicBezTo": {
            const pts = item["a:pt"] || [];
            if (pts.length === 3) {
              const [c1, c2, end] = pts;
              commands.push(`C ${pointStr(c1)} ${pointStr(c2)} ${pointStr(end)}`);
            }
            break;
          }
          
          case "a:close":
            commands.push("Z");
            break;
            
          default:
            // Log unknown commands for debugging
            console.log(`Unknown path command: ${key}`);
        }
      });
    }

    const pathData = commands.join(" ");
    console.log(`Generated SVG path: ${pathData.substring(0, 100)}${pathData.length > 100 ? '...' : ''}`);
    
    return `<path d="${pathData}" />`;
    
  } catch (error) {
    console.error("Error parsing custom path:", error);
    return "";
  }
}

// Parse preset geometry paths (for future enhancement)
export function parsePresetGeometry(
  prstGeom: any,
  targetWidth: number,
  targetHeight: number
): string {
  // This could be enhanced to handle preset geometry adjustments
  // For now, fall back to SVG_PATH_MAP
  return "";
}
