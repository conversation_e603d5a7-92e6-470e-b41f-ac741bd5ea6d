import J<PERSON>Z<PERSON> from "jszip";
import { parseStringPromise } from "xml2js";
import { PptxSlide, PptxParseResult } from "./types";
import { extractImagesFromPptx } from "./extractors/imageExtractor";
import { processShapeTreeElements } from "./processors/slideProcessor";

// Parse individual slide XML with image mapping
const parseSlideXml = async (
  slideXml: string,
  slideNumber: number,
  extractedImages: any[] = []
): Promise<PptxSlide> => {
  const slide: PptxSlide = {
    slideNumber,
    texts: [],
    images: [],
    shapes: [],
  };

  try {
    const result = await parseStringPromise(slideXml);
    const slideData = result["p:sld"];

    if (!slideData) {
      console.warn(`No slide data found for slide ${slideNumber}`);
      return slide;
    }

    const commonSlideData = slideData["p:cSld"]?.[0];
    if (!commonSlideData) {
      console.warn(`No common slide data found for slide ${slideNumber}`);
      return slide;
    }

    const shapeTree = commonSlideData["p:spTree"]?.[0];
    if (!shapeTree) {
      console.warn(`No shape tree found for slide ${slideNumber}`);
      return slide;
    }

    console.log(
      `🔍 Processing slide ${slideNumber}, shapeTree keys:`,
      Object.keys(shapeTree)
    );

    // Process all types of elements
    processShapeTreeElements(shapeTree, slide, extractedImages);

    return slide;
  } catch (error) {
    console.error(`❌ Error parsing slide ${slideNumber}:`, error);
    return {
      slideNumber,
      texts: [],
      images: [],
      shapes: [],
    };
  }
};

// Main PPTX parser function
export const parsePptx = async (arrayBuffer: ArrayBuffer): Promise<PptxParseResult> => {
  console.log("🚀 Starting PPTX parsing...");

  try {
    const zip = await JSZip.loadAsync(arrayBuffer);
    const slides: PptxSlide[] = [];

    // Extract all images from media folder
    console.log("🖼️ Extracting images from PPTX...");
    const extractedImages = await extractImagesFromPptx(zip);
    console.log(`✅ Extracted ${extractedImages.length} images`);

    // Get all slide files
    const slideFiles = Object.keys(zip.files)
      .filter((fileName) => fileName.startsWith("ppt/slides/slide"))
      .sort((a, b) => {
        const aNum = parseInt(a.match(/slide(\d+)\.xml$/)?.[1] || "0");
        const bNum = parseInt(b.match(/slide(\d+)\.xml$/)?.[1] || "0");
        return aNum - bNum;
      });

    console.log(`📄 Found ${slideFiles.length} slides:`, slideFiles);

    // Parse each slide
    for (let i = 0; i < slideFiles.length; i++) {
      const fileName = slideFiles[i];
      const slideNumber = i + 1;

      console.log(`\n📋 Processing slide ${slideNumber}: ${fileName}`);

      try {
        const slideXml = await zip.files[fileName].async("string");
        const slide = await parseSlideXml(slideXml, slideNumber, extractedImages);
        slides.push(slide);
        console.log(`✅ Completed slide ${slideNumber}: ${slide.texts.length} texts, ${slide.images.length} images, ${slide.shapes.length} shapes`);
      } catch (error) {
        console.error(`❌ Error processing slide ${slideNumber}:`, error);
        // Add empty slide to maintain order
        slides.push({
          slideNumber,
          texts: [],
          images: [],
          shapes: [],
        });
      }
    }

    // Extract metadata
    const metadata = {
      totalSlides: slides.length,
      title: undefined,
      author: undefined,
      createdDate: undefined,
    };

    // Try to extract presentation metadata
    try {
      const corePropsFile = zip.files["docProps/core.xml"];
      if (corePropsFile) {
        const corePropsXml = await corePropsFile.async("string");
        const corePropsResult = await parseStringPromise(corePropsXml);
        
        metadata.title = corePropsResult?.["cp:coreProperties"]?.["dc:title"]?.[0] || undefined;
        metadata.author = corePropsResult?.["cp:coreProperties"]?.["dc:creator"]?.[0] || undefined;
        metadata.createdDate = corePropsResult?.["cp:coreProperties"]?.["dcterms:created"]?.[0]?._ || undefined;
      }
    } catch (metadataError) {
      console.warn("⚠️ Could not extract metadata:", metadataError);
    }

    console.log(`\n🎉 PPTX parsing completed successfully!`);
    console.log(`📊 Summary: ${slides.length} slides, ${extractedImages.length} images`);
    console.log(`📈 Total elements: ${slides.reduce((total, slide) => total + slide.texts.length + slide.images.length + slide.shapes.length, 0)}`);

    return {
      slides,
      images: extractedImages,
      metadata,
    };
  } catch (error) {
    console.error("❌ Error parsing PPTX:", error);
    throw new Error(`Failed to parse PPTX: ${error}`);
  }
};

// Export types for external use
export * from "./types";
