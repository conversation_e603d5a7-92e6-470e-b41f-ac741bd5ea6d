// Extract text from paragraph runs
export const extractTextFromRuns = (runs: any[]): string => {
  if (!runs || runs.length === 0) return "";

  return runs
    .map((run: any) => {
      const textElement = run["a:t"];
      if (Array.isArray(textElement)) {
        return textElement.join("");
      }
      return textElement || "";
    })
    .join("");
};

// Extract text from node
export const extractTextFromNode = (node: any): string[] => {
  const texts: string[] = [];

  if (node["a:t"]) {
    if (Array.isArray(node["a:t"])) {
      texts.push(...node["a:t"]);
    } else {
      texts.push(node["a:t"]);
    }
  }

  if (node["a:r"]) {
    const runs = Array.isArray(node["a:r"]) ? node["a:r"] : [node["a:r"]];
    runs.forEach((run: any) => {
      if (run["a:t"]) {
        if (Array.isArray(run["a:t"])) {
          texts.push(...run["a:t"]);
        } else {
          texts.push(run["a:t"]);
        }
      }
    });
  }

  return texts;
};
