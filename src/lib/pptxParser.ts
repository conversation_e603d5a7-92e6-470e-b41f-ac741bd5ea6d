import J<PERSON>Z<PERSON> from "jszip";
import { parseStringPromise } from "xml2js";

export interface PptxSlide {
  slideNumber: number;
  texts: Array<{
    text: string;
    x?: number;
    y?: number;
    fontSize?: number;
    fontFamily?: string;
    color?: string;
  }>;
  images: Array<{
    src: string;
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    name?: string;
  }>;
  shapes: Array<{
    type: string;
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    fill?: string;
    border?: string;
    text?: string;
    svgPath?: string;
  }>;
}

export interface ExtractedImage {
  src: string; // base64 data URL
  name: string; // original filename
  path: string; // path in PPTX
  type: string; // image type (png, jpg, etc.)
}

export interface PptxParseResult {
  slides: PptxSlide[];
  images: ExtractedImage[]; // All extracted images
  metadata: {
    totalSlides: number;
    title?: string;
    author?: string;
    createdDate?: string;
  };
}

// Extract images from PPTX media folder
const extractImagesFromPptx = async (zip: JSZip): Promise<ExtractedImage[]> => {
  const images: ExtractedImage[] = [];

  try {
    // Find all files in ppt/media/ folder
    const mediaFiles = Object.keys(zip.files).filter(
      (path) => path.startsWith("ppt/media/") && !path.endsWith("/")
    );

    console.log("Found media files:", mediaFiles);

    for (const path of mediaFiles) {
      try {
        const file = zip.files[path];
        if (!file) continue;

        // Get file extension to determine image type
        const fileName = path.split("/").pop() || "";
        const fileExtension = fileName.split(".").pop()?.toLowerCase() || "";

        // Only process image files
        const imageExtensions = [
          "png",
          "jpg",
          "jpeg",
          "gif",
          "bmp",
          "svg",
          "webp",
        ];
        if (!imageExtensions.includes(fileExtension)) {
          console.log(`Skipping non-image file: ${path}`);
          continue;
        }

        // Convert to base64
        const fileData = await file.async("base64");
        const mimeType = getMimeType(fileExtension);

        images.push({
          src: `data:${mimeType};base64,${fileData}`,
          name: fileName,
          path: path,
          type: fileExtension,
        });

        console.log(`Extracted image: ${fileName} (${fileExtension})`);
      } catch (error) {
        console.error(`Error extracting image ${path}:`, error);
      }
    }
  } catch (error) {
    console.error("Error extracting images from PPTX:", error);
  }

  return images;
};

// Get MIME type for image extensions
const getMimeType = (extension: string): string => {
  const mimeTypes: { [key: string]: string } = {
    png: "image/png",
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    gif: "image/gif",
    bmp: "image/bmp",
    svg: "image/svg+xml",
    webp: "image/webp",
  };
  return mimeTypes[extension] || "image/png";
};

// Helper function to extract text from XML nodes
const extractTextFromNode = (node: any): string[] => {
  const texts: string[] = [];

  if (!node) return texts;

  // Handle different text structures in PPTX
  const processTextNode = (textNode: any) => {
    if (typeof textNode === "string") {
      texts.push(textNode);
    } else if (Array.isArray(textNode)) {
      textNode.forEach(processTextNode);
    } else if (textNode && typeof textNode === "object") {
      // Check for text content in various properties
      if (textNode._) {
        texts.push(textNode._);
      } else if (textNode.$text) {
        texts.push(textNode.$text);
      } else {
        // Recursively search for text in nested objects
        Object.values(textNode).forEach(processTextNode);
      }
    }
  };

  processTextNode(node);
  return texts;
};

// Generate SVG path for shape type
const generateSvgPath = (
  shapeType: string,
  width: number,
  height: number,
  childrenSvg?: string
): string => {
  const w = width;
  const h = height;

  switch (shapeType) {
    case "group":
      return `<g>${childrenSvg || ""}</g>`;

    case "roundRect":
      const rx = Math.min(w, h) * 0.1; // 10% radius
      return `<rect x="0" y="0" width="${w}" height="${h}" rx="${rx}" ry="${rx}" />`;

    case "triangle":
      return `<polygon points="${w / 2},0 ${w},${h} 0,${h}" />`;

    case "diamond":
      return `<polygon points="${w / 2},0 ${w},${h / 2} ${w / 2},${h} 0,${
        h / 2
      }" />`;

    case "rightArrow":
      return `<polygon points="0,${h * 0.3} ${w * 0.7},${h * 0.3} ${
        w * 0.7
      },0 ${w},${h / 2} ${w * 0.7},${h} ${w * 0.7},${h * 0.7} 0,${h * 0.7}" />`;

    case "leftArrow":
      return `<polygon points="0,${h / 2} ${w * 0.3},0 ${w * 0.3},${
        h * 0.3
      } ${w},${h * 0.3} ${w},${h * 0.7} ${w * 0.3},${h * 0.7} ${
        w * 0.3
      },${h}" />`;

    case "star5":
      const centerX = w / 2;
      const centerY = h / 2;
      const outerRadius = Math.min(w, h) / 2;
      const innerRadius = outerRadius * 0.4;
      let points = "";

      for (let i = 0; i < 10; i++) {
        const angle = (i * Math.PI) / 5 - Math.PI / 2;
        const radius = i % 2 === 0 ? outerRadius : innerRadius;
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);
        points += `${x},${y} `;
      }

      return `<polygon points="${points.trim()}" />`;

    case "plus":
      return `<path d="M ${w * 0.4} 0 L ${w * 0.6} 0 L ${w * 0.6} ${
        h * 0.4
      } L ${w} ${h * 0.4} L ${w} ${h * 0.6} L ${w * 0.6} ${h * 0.6} L ${
        w * 0.6
      } ${h} L ${w * 0.4} ${h} L ${w * 0.4} ${h * 0.6} L 0 ${h * 0.6} L 0 ${
        h * 0.4
      } L ${w * 0.4} ${h * 0.4} Z" />`;

    case "heart":
      return `<path d="M ${w / 2} ${h * 0.8} C ${w / 2} ${h * 0.8} 0 ${
        h * 0.4
      } 0 ${h * 0.25} C 0 ${h * 0.1} ${w * 0.25} 0 ${w / 2} ${h * 0.3} C ${
        w * 0.75
      } 0 ${w} ${h * 0.1} ${w} ${h * 0.25} C ${w} ${h * 0.4} ${w / 2} ${
        h * 0.8
      } ${w / 2} ${h * 0.8} Z" />`;

    case "hexagon":
      const hexCenterX = w / 2;
      const hexCenterY = h / 2;
      const hexPoints = [];
      for (let i = 0; i < 6; i++) {
        const angle = (i * Math.PI) / 3;
        const x = hexCenterX + (w / 2) * Math.cos(angle);
        const y = hexCenterY + (h / 2) * Math.sin(angle);
        hexPoints.push(`${x},${y}`);
      }
      return `<polygon points="${hexPoints.join(" ")}" />`;

    case "flowChartProcess":
      return `<rect x="0" y="0" width="${w}" height="${h}" rx="4" ry="4" />`;

    case "flowChartDecision":
      return `<polygon points="${w / 2},0 ${w},${h / 2} ${w / 2},${h} 0,${
        h / 2
      }" />`;

    default: // rect
      return `<rect x="0" y="0" width="${w}" height="${h}" />`;
  }
};

// Parse shape properties from XML
const parseShapeProperties = (spPr: any, childrenSvg?: string): any => {
  try {
    const xfrm = spPr["a:xfrm"]?.[0];
    if (!xfrm) return null;

    const off = xfrm["a:off"]?.[0]?.$;
    const ext = xfrm["a:ext"]?.[0]?.$;
    if (!off || !ext) return null;

    const x = Math.round(parseInt(off.x || "0") / 12700);
    const y = Math.round(parseInt(off.y || "0") / 12700);
    const width = Math.round(parseInt(ext.cx || "0") / 12700);
    const height = Math.round(parseInt(ext.cy || "0") / 12700);

    let shapeType = "rect";
    const prstGeom = spPr["a:prstGeom"]?.[0];
    if (prstGeom) {
      shapeType = prstGeom.$?.prst || "rect";
    }

    if (shapeType === "custom") shapeType = "rect";

    let fill = "#FFFFFF";
    const solidFill = spPr["a:solidFill"]?.[0];
    if (solidFill) {
      const srgbClr = solidFill["a:srgbClr"]?.[0]?.$?.val;
      if (srgbClr) {
        fill = `#${srgbClr}`;
      }
    }

    let border = "none";
    const ln = spPr["a:ln"]?.[0];
    if (ln) {
      const w = ln.$?.w ? Math.round(parseInt(ln.$.w) / 12700) : 1;
      const srgbClr = ln["a:solidFill"]?.[0]?.["a:srgbClr"]?.[0]?.$?.val;
      const borderColor = srgbClr ? `#${srgbClr}` : "#000000";
      border = `${w}px solid ${borderColor}`;
    }

    const svgPath = generateSvgPath(shapeType, width, height, childrenSvg);

    return {
      type: shapeType,
      x,
      y,
      width,
      height,
      fill,
      border,
      svgPath,
    };
  } catch (error) {
    console.error("Error parsing shape properties:", error);
    return null;
  }
};

const processGroupShape = (
  group: any,
  slide: PptxSlide,
  extractedImages: ExtractedImage[]
) => {
  const children: any[] = [];

  // Thu thập tất cả các SVG con trong group
  const shapeTree = group["p:grpSpPr"] ? group : group["p:spTree"] || group;

  const shapes = shapeTree["p:sp"] || [];
  shapes.forEach((shape: any) => {
    const spPr = shape["p:spPr"]?.[0];
    if (spPr) {
      const childShape = parseShapeProperties(spPr);
      if (childShape?.svgPath) {
        children.push({
          ...childShape,
          svg: childShape.svgPath,
        });
      }
    }
  });

  const childrenSvg = children.map((s) => s.svg).join("\n");
  const spPr = group["p:spPr"]?.[0] || group["p:grpSpPr"]?.[0];
  if (spPr) {
    const groupShape = parseShapeProperties(spPr, childrenSvg);
    if (groupShape) {
      slide.shapes.push(groupShape);
      console.log("Added group shape:", groupShape);
    }
  }
};

// Process all elements in shape tree (recursive for groups)
const processShapeTreeElements = (
  shapeTree: any,
  slide: PptxSlide,
  extractedImages: ExtractedImage[]
) => {
  const shapes = shapeTree["p:sp"] || [];
  shapes.forEach((shape: any) => processShape(shape, slide));

  const groupShapes = shapeTree["p:grpSp"] || [];
  groupShapes.forEach((group: any) => {
    console.log("Processing group shape");
    processGroupShape(group, slide, extractedImages);
  });

  const connectionShapes = shapeTree["p:cxnSp"] || [];
  connectionShapes.forEach((cxnShape: any) => {
    processShape(cxnShape, slide);
  });

  const graphicFrames = shapeTree["p:graphicFrame"] || [];
  graphicFrames.forEach((frame: any) => {
    processGraphicFrame(frame, slide);
  });

  const pictures = shapeTree["p:pic"] || [];
  pictures.forEach((picture: any, index: number) => {
    processPicture(picture, slide, extractedImages, index);
  });
};

// Process individual shape
const processShape = (shape: any, slide: PptxSlide) => {
  try {
    console.log("Processing shape:", Object.keys(shape));

    // Extract text from text body
    const textBody = shape["p:txBody"]?.[0];
    if (textBody) {
      console.log("Shape has text body");
      const paragraphs = textBody["a:p"] || [];

      paragraphs.forEach((paragraph: any) => {
        const runs = paragraph["a:r"] || [];
        const text = extractTextFromRuns(runs);

        if (text.trim()) {
          // Try to extract position from transform
          const spPr = shape["p:spPr"]?.[0];
          const position = extractPosition(spPr);

          slide.texts.push({
            text: text.trim(),
            x: position.x,
            y: position.y,
            fontSize: 12, // Could extract from run properties
            fontFamily: "Arial", // Default
            color: "#000000", // Default
          });
          console.log(
            `Added text: "${text.trim()}" at (${position.x}, ${position.y})`
          );
        }
      });
    }

    // ALWAYS try to extract shape properties (even if it has text)
    const spPr = shape["p:spPr"]?.[0];
    if (spPr) {
      console.log("Shape has spPr, attempting to parse shape properties");
      try {
        const shapeData = parseShapeProperties(spPr);
        if (shapeData) {
          // KHÔNG gộp text vào shape, để riêng biệt
          slide.shapes.push(shapeData);
          console.log(
            `Added shape: ${shapeData.type} at (${shapeData.x}, ${
              shapeData.y
            }) - SVG: ${shapeData.svgPath?.substring(0, 50)}...`
          );
        } else {
          console.log("parseShapeProperties returned null");
        }
      } catch (shapeError) {
        console.warn("Error parsing shape properties:", shapeError);
      }
    } else {
      console.log("Shape has no spPr");
    }
  } catch (err) {
    console.warn("Error processing shape:", err);
  }
};

// Process graphic frame (tables, charts)
const processGraphicFrame = (frame: any, slide: PptxSlide) => {
  try {
    // Check if it's a table
    const graphic = frame["a:graphic"]?.[0];
    const graphicData = graphic?.["a:graphicData"]?.[0];

    if (graphicData?.["a:tbl"]) {
      console.log("Found table in graphic frame");
      const table = graphicData["a:tbl"][0];
      const tableData = parseTable(table);

      if (tableData && tableData.length > 0) {
        // Add table as a special text element
        const position = extractPosition(frame["p:xfrm"]?.[0]);
        slide.texts.push({
          text: `[TABLE: ${tableData.length}x${tableData[0]?.length || 0}]`,
          x: position.x,
          y: position.y,
          fontSize: 12,
          fontFamily: "Arial",
          color: "#000000",
        });
        console.log(`Added table: ${tableData.length} rows`);
      }
    }
  } catch (err) {
    console.warn("Error processing graphic frame:", err);
  }
};

// Process picture element
const processPicture = (
  picture: any,
  slide: PptxSlide,
  extractedImages: ExtractedImage[],
  index: number
) => {
  try {
    const actualImage = extractedImages[index] || null;
    const position = extractPosition(picture["p:spPr"]?.[0]);

    slide.images.push({
      src: actualImage ? actualImage.src : "/images/placeholder-image.svg",
      x: position.x,
      y: position.y,
      width: 200, // Default size, could extract from spPr
      height: 150,
      name: actualImage ? actualImage.name : `Image ${index + 1}`,
    });

    console.log(
      `Added image: ${actualImage?.name || "placeholder"} at (${position.x}, ${
        position.y
      })`
    );
  } catch (err) {
    console.warn("Error processing picture:", err);
  }
};

// Extract transform from spPr (dùng chung cho position và group transform)
const extractTransform = (
  spPr: any
): { x: number; y: number; width?: number; height?: number } => {
  try {
    const xfrm = spPr?.["a:xfrm"]?.[0];
    const off = xfrm?.["a:off"]?.[0]?.$;
    const ext = xfrm?.["a:ext"]?.[0]?.$;

    const result: { x: number; y: number; width?: number; height?: number } = {
      x: 0,
      y: 0,
    };

    if (off) {
      result.x = Math.round(parseInt(off.x || "0") / 12700);
      result.y = Math.round(parseInt(off.y || "0") / 12700);
    }

    if (ext) {
      result.width = Math.round(parseInt(ext.cx || "0") / 12700);
      result.height = Math.round(parseInt(ext.cy || "0") / 12700);
    }

    return result;
  } catch (err) {
    console.warn("Error extracting transform:", err);
  }

  return { x: 0, y: 0 };
};

// Extract position from transform (backward compatibility)
const extractPosition = (spPr: any): { x: number; y: number } => {
  const transform = extractTransform(spPr);
  return { x: transform.x, y: transform.y };
};

// Parse table data
const parseTable = (table: any): string[][] | null => {
  try {
    const rows = table["a:tr"] || [];
    const tableData: string[][] = [];

    rows.forEach((row: any) => {
      const cells = row["a:tc"] || [];
      const rowData: string[] = [];

      cells.forEach((cell: any) => {
        const txBody = cell["a:txBody"]?.[0];
        if (txBody) {
          const paragraphs = txBody["a:p"] || [];
          let cellText = "";

          paragraphs.forEach((paragraph: any) => {
            const runs = paragraph["a:r"] || [];
            cellText += extractTextFromRuns(runs);
          });

          rowData.push(cellText.trim());
        } else {
          rowData.push("");
        }
      });

      if (rowData.length > 0) {
        tableData.push(rowData);
      }
    });

    return tableData.length > 0 ? tableData : null;
  } catch (err) {
    console.warn("Error parsing table:", err);
    return null;
  }
};

// Extract text from paragraph runs
const extractTextFromRuns = (runs: any[]): string => {
  if (!Array.isArray(runs)) return "";

  const textParts: string[] = [];

  runs.forEach((run) => {
    if (run["a:t"]) {
      const textNodes = Array.isArray(run["a:t"]) ? run["a:t"] : [run["a:t"]];
      textNodes.forEach((textNode) => {
        if (typeof textNode === "string") {
          textParts.push(textNode);
        } else if (textNode && textNode._) {
          textParts.push(textNode._);
        }
      });
    }
  });

  return textParts.join("");
};

// Parse individual slide XML with image mapping
const parseSlideXml = async (
  slideXml: string,
  slideNumber: number,
  extractedImages: ExtractedImage[] = []
): Promise<PptxSlide> => {
  try {
    const result = await parseStringPromise(slideXml);
    const slide: PptxSlide = {
      slideNumber,
      texts: [],
      images: [],
      shapes: [],
    };

    // Navigate through the slide structure
    const slideData = result?.["p:sld"];
    if (!slideData) return slide;

    const commonSlideData = slideData["p:cSld"]?.[0];
    if (!commonSlideData) return slide;

    const shapeTree = commonSlideData["p:spTree"]?.[0];
    if (!shapeTree) return slide;

    // Process all elements in the shape tree
    console.log(
      `Processing slide ${slideNumber}, shapeTree keys:`,
      Object.keys(shapeTree)
    );

    // Process all types of elements
    processShapeTreeElements(shapeTree, slide, extractedImages);

    return slide;
  } catch (error) {
    console.error(`Error parsing slide ${slideNumber}:`, error);
    return {
      slideNumber,
      texts: [],
      images: [],
      shapes: [],
    };
  }
};

// Main parser function
export const parsePptx = async (
  arrayBuffer: ArrayBuffer
): Promise<PptxParseResult> => {
  try {
    const zip = await JSZip.loadAsync(arrayBuffer);
    const slides: PptxSlide[] = [];

    // Extract all images from media folder
    console.log("Extracting images from PPTX...");
    const extractedImages = await extractImagesFromPptx(zip);
    console.log(`Extracted ${extractedImages.length} images`);

    // Get all slide files
    const slideFiles = Object.keys(zip.files)
      .filter(
        (fileName) =>
          fileName.startsWith("ppt/slides/slide") &&
          fileName.endsWith(".xml") &&
          !fileName.includes("_rels")
      )
      .sort((a, b) => {
        // Sort by slide number
        const aNum = parseInt(a.match(/slide(\d+)\.xml$/)?.[1] || "0");
        const bNum = parseInt(b.match(/slide(\d+)\.xml$/)?.[1] || "0");
        return aNum - bNum;
      });

    console.log("Found slide files:", slideFiles);

    // Parse each slide
    for (let i = 0; i < slideFiles.length; i++) {
      const fileName = slideFiles[i];
      const slideNumber = i + 1;

      try {
        const slideXml = await zip.files[fileName].async("string");
        const slide = await parseSlideXml(
          slideXml,
          slideNumber,
          extractedImages
        );
        slides.push(slide);
      } catch (error) {
        console.error(`Error processing slide ${slideNumber}:`, error);
        // Add empty slide to maintain numbering
        slides.push({
          slideNumber,
          texts: [],
          images: [],
          shapes: [],
        });
      }
    }

    // Extract metadata from core properties if available
    let metadata = {
      totalSlides: slides.length,
      title: undefined as string | undefined,
      author: undefined as string | undefined,
      createdDate: undefined as string | undefined,
    };

    try {
      const corePropsFile = zip.files["docProps/core.xml"];
      if (corePropsFile) {
        const corePropsXml = await corePropsFile.async("string");
        const coreProps = await parseStringPromise(corePropsXml);

        metadata.title =
          coreProps?.["cp:coreProperties"]?.["dc:title"]?.[0] || undefined;
        metadata.author =
          coreProps?.["cp:coreProperties"]?.["dc:creator"]?.[0] || undefined;
        metadata.createdDate =
          coreProps?.["cp:coreProperties"]?.["dcterms:created"]?.[0]?._ ||
          undefined;
      }
    } catch (error) {
      console.warn("Could not extract metadata:", error);
    }

    return {
      slides,
      images: extractedImages,
      metadata,
    };
  } catch (error) {
    console.error("Error parsing PPTX file:", error);
    throw new Error(
      "Không thể đọc file PPTX. Vui lòng kiểm tra định dạng file."
    );
  }
};

// Utility function to extract all text from slides
export const extractAllText = (slides: PptxSlide[]): string => {
  return slides
    .map((slide) => slide.texts.map((t) => t.text).join(" "))
    .filter((text) => text.trim().length > 0)
    .join("\n\n");
};

// Utility function to get slide statistics
export const getSlideStatistics = (slides: PptxSlide[]) => {
  return {
    totalSlides: slides.length,
    totalTexts: slides.reduce((total, slide) => total + slide.texts.length, 0),
    totalImages: slides.reduce(
      (total, slide) => total + slide.images.length,
      0
    ),
    totalShapes: slides.reduce(
      (total, slide) => total + slide.shapes.length,
      0
    ),
    averageTextsPerSlide:
      slides.length > 0
        ? Math.round(
            (slides.reduce((total, slide) => total + slide.texts.length, 0) /
              slides.length) *
              10
          ) / 10
        : 0,
  };
};
