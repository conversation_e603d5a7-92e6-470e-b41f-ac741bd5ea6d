{"name": "planbookfe", "version": "0.1.0", "private": true, "scripts": {"prisma:generate": "prisma generate", "dev": "next dev --turbopack", "build": "npx prisma generate --schema=src/prisma/schema.prisma && next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.9.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-icons/all-files": "^4.1.0", "@solana/wallet-standard-features": "^1.3.0", "@stomp/stompjs": "^7.1.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.80.6", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-image": "^2.24.2", "@tiptap/extension-link": "^2.24.2", "@tiptap/react": "^2.24.2", "@tiptap/starter-kit": "^2.24.2", "@types/file-saver": "^2.0.7", "@types/uuid": "^10.0.0", "antd": "^5.25.2", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "docx": "^9.5.1", "file-saver": "^2.0.5", "jszip": "^3.10.1", "lucide-react": "^0.511.0", "mammoth": "^1.9.1", "matter-js": "^0.20.0", "nanoid": "^5.1.5", "next": "15.3.2", "next-themes": "^0.4.6", "pdfjs-dist": "^5.3.31", "pptx-parser": "^1.1.7-beta.9", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-draggable": "^4.5.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-pdf": "^10.0.1", "react-resizable": "^3.0.5", "react-rnd": "^10.5.2", "react-scroll-parallax": "^3.4.5", "recharts": "^3.1.0", "sass": "^1.89.0", "sockjs-client": "^1.6.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "three": "^0.178.0", "uuid": "^11.1.0", "wavesurfer.js": "^7.9.9", "xml2js": "^0.6.2", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.7", "@types/jquery": "^3.5.32", "@types/jszip": "^3.4.0", "@types/matter-js": "^0.19.8", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/sockjs-client": "^1.5.4", "@types/xml2js": "^0.4.14", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "prisma": "^6.9.0", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "^5"}, "prisma": {"schema": "src/prisma/schema.prisma"}}