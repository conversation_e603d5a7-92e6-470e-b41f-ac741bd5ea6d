# PPTX Import Guide

## 📋 Tổng quan

Hệ thống PPTX Import cho phép người dùng upload file PowerPoint (.pptx) và chuyển đổi thành JSON để hiển thị trên web. Hệ thống sử dụng thư viện `jszip` và `xml2js` để parse nội dung file PPTX.

## 🚀 Cài đặt

### Dependencies đã được cài đặt:
```bash
npm install jszip xml2js
npm install --save-dev @types/jszip @types/xml2js
```

## 📁 Cấu trúc Files

```
src/
├── lib/
│   └── pptxParser.ts                    # Core parser logic
├── components/
│   ├── organisms/
│   │   └── pptx-upload/                 # Full-featured upload component
│   └── molecules/
│       └── pptx-upload-simple/          # Simple upload component
├── hooks/
│   └── usePptxImport.ts                 # React hook for PPTX data management
└── app/(tools)/
    ├── pptx-demo/                       # Full demo page
    └── pptx-simple/                     # Simple demo page
```

## 🔧 Components

### 1. PptxUpload (Full-featured)
**Path:** `src/components/organisms/pptx-upload/index.tsx`

```tsx
import PptxUpload, { PptxSlide } from "@/components/organisms/pptx-upload";

<PptxUpload
  onSlidesChange={(slides) => console.log(slides)}
  onError={(error) => console.error(error)}
  showPreview={true}
  maxFileSize={50}
  className="my-custom-class"
/>
```

**Props:**
- `onSlidesChange?: (slides: PptxSlide[]) => void` - Callback khi slides thay đổi
- `onError?: (error: string) => void` - Callback khi có lỗi
- `showPreview?: boolean` - Hiển thị preview slides (default: true)
- `maxFileSize?: number` - Kích thước file tối đa (MB, default: 50)
- `className?: string` - CSS class tùy chỉnh

### 2. PptxUploadSimple (Lightweight)
**Path:** `src/components/molecules/pptx-upload-simple/index.tsx`

```tsx
import PptxUploadSimple from "@/components/molecules/pptx-upload-simple";

<PptxUploadSimple
  onSlidesChange={(slides) => console.log(slides)}
  onError={(error) => console.error(error)}
  maxFileSize={50}
  placeholder="Upload your PPTX file"
  showFileInfo={true}
/>
```

**Props:**
- `onSlidesChange?: (slides: PptxSlide[]) => void`
- `onError?: (error: string) => void`
- `maxFileSize?: number` - Default: 50MB
- `placeholder?: string` - Text hiển thị trong upload area
- `showFileInfo?: boolean` - Hiển thị thông tin file (default: true)

## 🎣 Hook Usage

### usePptxImport Hook
**Path:** `src/hooks/usePptxImport.ts`

```tsx
import { usePptxImport } from "@/hooks/usePptxImport";

function MyComponent() {
  const {
    slides,
    isLoading,
    error,
    fileName,
    setSlides,
    setError,
    exportToJson,
    downloadJson,
    getAllText,
    getStatistics,
    reset
  } = usePptxImport();

  return (
    <div>
      <PptxUploadSimple onSlidesChange={setSlides} onError={setError} />
      {slides.length > 0 && (
        <div>
          <p>Slides: {getStatistics().totalSlides}</p>
          <button onClick={() => downloadJson()}>Download JSON</button>
          <button onClick={() => copyToClipboard(getAllText())}>Copy Text</button>
        </div>
      )}
    </div>
  );
}
```

## 📊 Data Structure

### PptxSlide Interface
```typescript
interface PptxSlide {
  slideNumber: number;
  texts: Array<{
    text: string;
    x?: number;
    y?: number;
    fontSize?: number;
    fontFamily?: string;
    color?: string;
  }>;
  images: Array<{
    src: string;
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    name?: string;
  }>;
  shapes: Array<{
    type: string;
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    fill?: string;
  }>;
}
```

### PptxParseResult Interface
```typescript
interface PptxParseResult {
  slides: PptxSlide[];
  metadata: {
    totalSlides: number;
    title?: string;
    author?: string;
    createdDate?: string;
  };
}
```

## 🔍 Core Parser

### parsePptx Function
**Path:** `src/lib/pptxParser.ts`

```tsx
import { parsePptx } from "@/lib/pptxParser";

const handleFileUpload = async (file: File) => {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const result = await parsePptx(arrayBuffer);
    
    console.log("Slides:", result.slides);
    console.log("Metadata:", result.metadata);
  } catch (error) {
    console.error("Parse error:", error);
  }
};
```

## 🎯 Demo Pages

### 1. Full Demo
**URL:** `http://localhost:3002/pptx-demo`
- Hiển thị đầy đủ tính năng
- Preview slides chi tiết
- JSON output với syntax highlighting
- Statistics và metadata

### 2. Simple Demo
**URL:** `http://localhost:3002/pptx-simple`
- Giao diện đơn giản, gọn nhẹ
- Tập trung vào chức năng chính
- Phù hợp để tích hợp vào các trang khác

## ⚠️ Limitations

1. **Text Support:** Chỉ hỗ trợ text cơ bản, không hỗ trợ formatting phức tạp
2. **Images:** Chỉ detect được số lượng và vị trí, không extract được nội dung ảnh
3. **Shapes:** Hỗ trợ cơ bản, không hỗ trợ shapes phức tạp
4. **Animations:** Không hỗ trợ animations và transitions
5. **Charts/Tables:** Không hỗ trợ charts và tables nâng cao

## 🔧 Customization

### Tùy chỉnh Parser
Để mở rộng khả năng parse, chỉnh sửa file `src/lib/pptxParser.ts`:

```typescript
// Thêm logic parse cho elements mới
const parseCustomElement = (element: any) => {
  // Custom parsing logic
};
```

### Tùy chỉnh UI
Tạo component mới dựa trên các component có sẵn:

```tsx
import { parsePptx } from "@/lib/pptxParser";

const MyCustomUploader = () => {
  // Custom implementation
};
```

## 🐛 Troubleshooting

### Lỗi thường gặp:

1. **"Không thể đọc file PPTX"**
   - Kiểm tra file có đúng định dạng .pptx không
   - Kiểm tra file có bị corrupt không

2. **"File quá lớn"**
   - Tăng `maxFileSize` prop
   - Nén file PPTX trước khi upload

3. **"Không parse được text"**
   - File PPTX có thể sử dụng format không chuẩn
   - Thử export lại từ PowerPoint

## 📝 Examples

### Basic Usage
```tsx
import PptxUploadSimple from "@/components/molecules/pptx-upload-simple";

const MyPage = () => {
  const handleSlides = (slides) => {
    console.log("Received slides:", slides);
  };

  return (
    <PptxUploadSimple
      onSlidesChange={handleSlides}
      maxFileSize={25}
      placeholder="Drop your presentation here"
    />
  );
};
```

### Advanced Usage with Hook
```tsx
import { usePptxImport } from "@/hooks/usePptxImport";
import PptxUpload from "@/components/organisms/pptx-upload";

const AdvancedPage = () => {
  const { slides, setSlides, exportToJson, getStatistics } = usePptxImport();
  
  const stats = getStatistics();
  
  return (
    <div>
      <PptxUpload onSlidesChange={setSlides} />
      {slides.length > 0 && (
        <div>
          <h3>Statistics:</h3>
          <p>Slides: {stats.totalSlides}</p>
          <p>Texts: {stats.totalTexts}</p>
          <p>Images: {stats.totalImages}</p>
          <p>Shapes: {stats.totalShapes}</p>
          
          <button onClick={() => console.log(exportToJson())}>
            Export JSON
          </button>
        </div>
      )}
    </div>
  );
};
```

## 🚀 Next Steps

1. **Tích hợp vào lesson plan system**
2. **Thêm hỗ trợ cho charts và tables**
3. **Cải thiện text formatting detection**
4. **Thêm image extraction**
5. **Optimize performance cho file lớn**
